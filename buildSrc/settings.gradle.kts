dependencyResolutionManagement {

    @Suppress("UnstableApiUsage")
    repositories {
        maven {
            url = uri("http://192.168.110.210/nexus/repository/maven-public/")
            isAllowInsecureProtocol = true
        }
    }

    // Reuse the version catalog from the main build.
    versionCatalogs {
        create("libs") {
            from(files("../gradle/libs.versions.toml"))
        }
    }
}

rootProject.name = "buildSrc"