plugins {
    id("buildsrc.kotlin-jvm")
    id("org.openjfx.javafxplugin") version "0.1.0"
    application
}

application {
    mainClass = "com.chipon.configtool.app.App"
}

dependencies {
    implementation(project(":config-core"))
    implementation(project(":config-net"))

    implementation(libs.kotlinxCoroutines)
    implementation(libs.tornadofx) {
        isChanging = true
    }
    implementation(libs.atlantafx)
    //implementation(libs.controlsfx)
    implementation("org.kordamp.ikonli:ikonli-materialdesign2-pack:12.4.0")
    implementation("org.kordamp.ikonli:ikonli-javafx:12.4.0")

    implementation(files("libs/scenicview.jar"))
}

javafx {
    version = "21.0.7"
    modules = listOf("javafx.controls", "javafx.graphics", "javafx.web", "javafx.fxml")
    version = "21.0.7"
    configurations = arrayOf("implementation", "testImplementation")
}
