package com.chipon.configtool.app

import atlantafx.base.theme.CupertinoDark
import atlantafx.base.theme.CupertinoLight
import javafx.application.Application.setUserAgentStylesheet

/**
 * <AUTHOR>
 */

fun applyTheme(value: String?) {
    when (value) {
        "Dark" -> setUserAgentStylesheet(CupertinoDark().userAgentStylesheet)
        else -> setUserAgentStylesheet(CupertinoLight().userAgentStylesheet)
    }
}