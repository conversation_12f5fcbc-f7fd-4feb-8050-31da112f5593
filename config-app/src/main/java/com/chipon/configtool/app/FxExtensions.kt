package com.chipon.configtool.app

import javafx.event.EventTarget
import javafx.geometry.Pos
import javafx.scene.control.TabPane
import javafx.scene.layout.VBox
import org.kordamp.ikonli.Ikon
import org.kordamp.ikonli.javafx.FontIcon
import tornadofx.*

/**
 * <AUTHOR>
 */

fun EventTarget.fixedtabpane(op: TabPane.() -> Unit = {}) =
    TabPane().attachTo(this, op).apply { tabClosingPolicy = TabPane.TabClosingPolicy.UNAVAILABLE }

fun EventTarget.container(op: VBox.() -> Unit = {}): VBox {
    val vBox = VBox()
    vBox.padding = insets(10.0)
    vBox.alignment = Pos.TOP_RIGHT
    opcr(this, vBox, op)
    return vBox
}

/**
 * https://kordamp.org/ikonli/cheat-sheet-materialdesign2.html
 */
fun icon(iconCode: Ikon): FontIcon {
   return FontIcon(iconCode)
}
