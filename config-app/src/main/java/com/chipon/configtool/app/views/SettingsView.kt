package com.chipon.configtool.app.views

import com.chipon.configtool.app.applyTheme
import javafx.beans.property.SimpleStringProperty
import tornadofx.*

/**
 * <AUTHOR>
 */
class SettingsView : View("Settings") {

    val theme = SimpleStringProperty(true, THEME, config.string(THEME)).apply {
        addListener { _, _, newValue ->
            with(app.config) {
                set(THEME to newValue)
                save()
            }
            runLater {
                applyTheme(newValue)
            }
        }
    }

    override val root = form {
        fieldset {
            field("主题切换") {
                combobox(
                    property = theme,
                    values = mutableListOf("Light", "Dark")
                )
            }
        }
    }

    companion object {
        const val THEME = "theme"
    }
}
