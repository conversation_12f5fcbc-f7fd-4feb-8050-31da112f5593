package com.chipon.configtool.app.model

import atlantafx.base.theme.PrimerDark
import atlantafx.base.theme.PrimerLight
import javafx.application.Application.setUserAgentStylesheet
import tornadofx.JsonModelAuto
import tornadofx.runLater
import tornadofx.stringProperty

/**
 * <AUTHOR>
 */
object AppConfig {

    val theme = stringProperty().apply {
        addListener { _, _, newValue ->
            runLater {
                when (newValue) {
                    "Default", "Light" -> setUserAgentStylesheet(PrimerLight().userAgentStylesheet)
                    "Dark" -> setUserAgentStylesheet(PrimerDark().userAgentStylesheet)
                }
            }
        }
    }

}
