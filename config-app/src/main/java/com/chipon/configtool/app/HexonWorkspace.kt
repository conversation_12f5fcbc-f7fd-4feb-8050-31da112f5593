package com.chipon.configtool.app

import ch.qos.logback.classic.LoggerContext
import com.chipon.configtool.app.Styles.Companion.console
import com.chipon.configtool.app.views.EmptyView
import com.chipon.configtool.app.views.SettingsView
import com.chipon.configtool.core.logger
import javafx.application.Platform
import javafx.geometry.Orientation
import javafx.scene.layout.VBox
import org.kordamp.ikonli.materialdesign2.MaterialDesignC
import org.scenicview.ScenicView
import org.slf4j.LoggerFactory
import tornadofx.*

/**
 * <AUTHOR> Wu
 */
class HexonWorkspace : Workspace(APP_NAME) {

    init {
        applyTheme(app.config.string(SettingsView.THEME))
        //remove toolbar
        (root.top as VBox).children.remove(header)
        menubar {
            menu("File") {
                item("Save", graphic = icon(MaterialDesignC.CONTENT_SAVE)).action {
                    logger.info("data saved successfully")
                }
                item("Save data as...", graphic = icon(MaterialDesignC.CONTENT_SAVE_PLUS)).action {
                }
                separator()
                item("Exit").action {
                    Platform.exit()
                }
            }
            menu("Extra") {
                item("Load profile").action {

                }
                item("Settings").action {
                    find<SettingsView>().openModal(resizable = false)
                }
            }
            menu("Help") {
                if (DEBUG_MODE) {
                    item("Inspector").action {
                        ScenicView.show(root)
                    }
                }

                item("About").action {
                    information(null, "Current version :${APP_VERSION}")
                }
            }
        }

        with(bottomDrawer) {
            item("Profiles") {
                hbox {

                }
            }
            item("Logs") {
                textarea {
                    addClass(console)
                    isWrapText = true

                    val context = LoggerFactory.getILoggerFactory() as LoggerContext
                    val appender = ConsoleViewAppender(context, this)
                    appender.start()
                }
            }
        }
    }

    override fun onBeforeShow() {
        workspace.dock(EmptyView, true)
    }

    class MainWindow : UIComponent("Hexon") {

        override val root = splitpane(
            Orientation.VERTICAL,
            vbox {
            }
        )

    }

}
