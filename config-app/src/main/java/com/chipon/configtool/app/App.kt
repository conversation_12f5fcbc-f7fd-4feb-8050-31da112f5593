package com.chipon.configtool.app

import javafx.application.Application.launch
import javafx.scene.image.Image
import tornadofx.*
import java.text.SimpleDateFormat


/**
 * <AUTHOR>
 */
class App : tornadofx.App(
    icon = Image(App::class.java.classLoader.getResourceAsStream("icon.png")),
) {

    override val primaryView = HexonWorkspace::class

    init {
        importStylesheet(Styles::class)
    }
}

const val APP_NAME = "Hexon"
const val APP_VERSION = "1.0.0"
val DEBUG_MODE = java.lang.management.ManagementFactory.getRuntimeMXBean()
    .inputArguments.any { it.contains("-agentlib:jdwp") }

val DATE_FORMAT = SimpleDateFormat("yyyy-MM-dd.HH:mm:ss")

fun main(args: Array<String>) {
    launch(App::class.java, *args)
}