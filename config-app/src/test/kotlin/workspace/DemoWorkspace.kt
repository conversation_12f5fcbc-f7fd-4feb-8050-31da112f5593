package no.tornado.fxsample.workspace

import javafx.application.Platform
import javafx.scene.Scene
import javafx.scene.control.Menu
import javafx.scene.control.MenuItem
import javafx.scene.layout.VBox
import org.scenicview.ScenicView
import tornadofx.*
import java.io.PrintStream

/**
 * Created by <PERSON><PERSON><PERSON> on 04/09/2017.
 */
open class DemoWorkspace : Workspace("Editor", navigationMode = NavigationMode.Tabs) {
    private val editorController: EditorController by inject()

    init {
        (root.top as VBox).children.remove(header)
        menubar {
            menu("File") {
                item("New") { id = newMenuItemId }.action {
                    //workspace.dock(mainView, true)
                    log.info("Opening text file")
                    workspace.dock(editorController.newEditor(), true)
                }
                separator()
                item("Exit").action {
                    log.info("Leaving workspace")
                    Platform.exit()
                }
            }
            menu("Window"){
                item("Close all").action {
                    editorController.editorModelList.clear()
                    workspace.dock(EmptyView(),true)
                }
                separator()
            }
            menu("Help") {
                item("About...")
            }

        }

        with(bottomDrawer) {
            item( "Logs") {
                textarea {
                    addClass("consola")
                    val ps = PrintStream(TextAreaOutputStream(this))
                    System.setErr(ps)
                    System.setOut(ps)
                }

            }
        }
    }

    companion object {
        const val newMenuItemId = "newFile"
    }

}
