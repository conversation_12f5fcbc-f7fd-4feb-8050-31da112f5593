# config_tool

This project uses [Gradle](https://gradle.org/).
To build and run the application, use the *Gradle* tool window by clicking the
Gradle icon in the right-hand toolbar,
or run it directly from the terminal:

* Run `./gradlew run` to build and run the application.
* Run `./gradlew build` to only build the application.
* Run `./gradlew check` to run all checks, including tests.
* Run `./gradlew clean` to clean all build outputs.

Note the usage of the Gradle Wrapper (`./gradlew`).
This is the suggested way to use Gradle in production projects.

This project uses a version catalog (see `gradle/libs.versions.toml`) to declare
and version dependencies

## 统一格式化文件导入
* 路径: File | Settings | Editor | Code Style -> Import Scheme
* 文件: [source_formatter.xml](source_formatter.xml)

## 使用Jetbrains JDK 支持更多的Hot reload场景
下载JBRSDK  https://github.com/JetBrains/JetBrainsRuntime/releases/tag/jbr-release-21.0.7b1038.54#:~:text=jbrsdk%2D21.0.7%2Dwindows%2Dx64%2Db1038.54.zip
vmargs: -XX:+AllowEnhancedClassRedefinition