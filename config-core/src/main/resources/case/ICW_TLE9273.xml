<?xml version="1.0" encoding="UTF-8"?>
<ifxmlcfg version="2.0.4">
	<checksum>f372de8e5b804d0d3e997d9a7ac4f3fa35060baabdea2cfe20bd638a2340de7e</checksum>
	<version label="V0.0.7"/>
	<fwversion min="2.2.1" max="*.*.*"/>
	<verticalcontainer color="255;255;255;255" sizepolicy="fixed;fixed">
		<var define="UIO.VERSION"/>
		<var define="UIO.MAINVERSION"/>
		<var define="UIO.SUBVERSION"/>
		<var define="USB.ERROR_CODE"/>		<!-- Required for storing 1st byte of SPI return value (0x11) = uIO-Stick available -->
		<var define="LED.GPIO"/>			<!-- Required for reading hardware status of all pins -->
		<var define="CTRL.M_S_CTRL[7:6]"/>	<!-- Required for setting the Mode via buttons -->
		<var define="CTRL.M_S_CTRL"/>		<!-- Required for read back of M_S_CTRL register via SPI and displaying mode -->
		<var define="CTRL.SUP_STAT"/>
		<var define="CTRL.THERM_STAT"/>
		<var define="CTRL.DEV_STAT"/>
		<var define="CTRL.BUS_STAT_1"/>
		<var define="CTRL.BUS_STAT_2"/>
		<var define="CTRL.WK_STAT_1"/>
		<var define="CTRL.WK_STAT_2"/>
		<var define="CTRL.WK_LVL_STAT"/>
		<var define="CTRL.SMPS_STAT"/>
		<var define="CTRL.FAM_PROD_STAT"/>	<!-- Required for display of product type and mode -->
		<timer visible="false" define="INIT" interval="1" singleShot="1" run="1">
			<action event="changed" cmd="sendUSB" data="0x01;0x13;0x55;0xAA"/>			<!-- Reset uIO to default values -->
			<action event="changed" cmd="sendUSB" data="0x02;0x41;0x0a"/>				<!-- SPI Baudrate 1 MHz -->
			<action event="changed" cmd="sendUSB" data="0x02;0x42"/>					<!-- SPI: passive clock level is low, receive on rising edge -->
			<action event="changed" cmd="sendUSB" data="0x02;0x46"/>					<!-- SPI: send MSB first -->
			<action event="changed" cmd="sendUSB" data="0x02;0x48;0x10"/>				<!-- SPI: word length 16 bit -->
			<action event="changed" cmd="sendUSB" data="0x06;0x10;0x01;0x00;0xB4"/>		<!-- Set SBC Watchdog trigger to 180ms -->
			<action event="changed" cmd="sendUSB" data="0x04;0x36;0;20"/>				<!-- Set GPIO Read&Keep for GPIO0 with 20*100ms hold time-->
			<action event="changed" cmd="sendUSB" data="0x04;0x36;1;20"/>				<!-- Set GPIO Read&Keep for GPIO1 with 20*100ms hold time -->
			<action event="changed" cmd="sendUSB" data="0x04;0x36;3;20"/>				<!-- Set GPIO Read&Keep for GPIO3 with 20*100ms hold time -->
			<action event="changed" cmd="sendUSB" data="0x01;0x10" recdata="?;?;UIO.VERSION;UIO.MAINVERSION;UIO.SUBVERSION"/>
		</timer>
		<timer visible="false" define="TIMER_M_S_CTRL" interval="500" singleShot="0" run="1">
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x7E" recdata="?;?;CTRL.FAM_PROD_STAT"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x01" recdata="?;?;CTRL.M_S_CTRL"/>
			<action event="changed" cmd="sendUSB" data="0x04;0x38" recdata="USB.ERROR_CODE;?;LED.GPIO"/><!-- read all GPIO pins -->
		</timer>
		<timer visible="false" define="TIMER_STATUS_READ" interval="500" singleShot="0" run="1">
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x41" recdata="?;?;CTRL.SUP_STAT"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x42" recdata="?;?;CTRL.THERM_STAT"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x43" recdata="?;?;CTRL.DEV_STAT"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x44" recdata="?;?;CTRL.BUS_STAT_1"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x45" recdata="?;?;CTRL.BUS_STAT_2"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x46" recdata="?;?;CTRL.WK_STAT_1"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x47" recdata="?;?;CTRL.WK_STAT_2"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x48" recdata="?;?;CTRL.WK_LVL_STAT"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x4C" recdata="?;?;CTRL.SMPS_STAT"/>
		</timer>
		<math visible="false" define="USB.M_S_CTRL" formula="CTRL.M_S_CTRL[7:6]*0x40+CTRL.M_S_CTRL[4:3]*0x08+CTRL.M_S_CTRL[2]*0x04+CTRL.M_S_CTRL[1:0]">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.M_S_CTRL;0x81"/>
		</math>
		<math visible="false" define="USB.HW_CTRL" formula="CTRL.HW_CTRL[7]*0x80+CTRL.HW_CTRL[6]*0x40+CTRL.HW_CTRL[5]*0x20+CTRL.HW_CTRL[4]*0x10+CTRL.HW_CTRL[3]*0x08+CTRL.HW_CTRL[2]*0x04+CTRL.HW_CTRL[1]*0x02+CTRL.HW_CTRL[0]">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.HW_CTRL;0x82"/>
		</math>
		<math visible="false" define="USB.WD_CTRL" format="%02x" formula="(CTRL.WD_CTRL[5]+CTRL.WD_CTRL[4]+CTRL.WD_CTRL[3]+1).*1 ? 0x80+CTRL.WD_CTRL[5]*0x20+CTRL.WD_CTRL[4]*0x10+CTRL.WD_CTRL[3]*0x08+4 : CTRL.WD_CTRL[5]*0x20+CTRL.WD_CTRL[4]*0x10+CTRL.WD_CTRL[3]*0x08+4">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.WD_CTRL;0x83"/>
		</math>
		<math visible="false" define="USB.BUS_CTRL_1" format="%02x" formula="CTRL.BUS_CTRL_1[7]*0x80+CTRL.BUS_CTRL_1[6]*0x40+CTRL.BUS_CTRL_1[5]*0x20+CTRL.BUS_CTRL_1[4:3]*0x08+CTRL.BUS_CTRL_1[1:0]">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.BUS_CTRL_1;0x84"/>
		</math>
		<math visible="false" define="USB.BUS_CTRL_2" format="%02x" formula="CTRL.BUS_CTRL_2[7:6]*0x40+CTRL.BUS_CTRL_2[4:3]*0x08+CTRL.BUS_CTRL_2[1:0]">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.BUS_CTRL_2;0x85"/>
		</math>
		<math visible="false" define="USB.WK_CTRL_1" format="%02x" formula="CTRL.WK_CTRL_1[6]*0x40">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.WK_CTRL_1;0x86"/>
		</math>
		<math visible="false" define="USB.WK_CTRL_2" format="%02x" formula="CTRL.WK_CTRL_2[0]">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.WK_CTRL_2;0x87"/>
		</math>
		<math visible="false" define="USB.WK_PUPD_CTRL" format="%02x" formula="CTRL.WK_PUPD_CTRL[1:0]">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.WK_PUPD_CTRL;0x88"/>
		</math>
		<math visible="false" define="USB.TIMER1_CTRL" format="%02x" formula="CTRL.TIMER1_CTRL[2:0]">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.TIMER1_CTRL;0x8C"/>
		</math>
		<header file="SBC_TLE9263.h">
			<gridcontainer columns="2">
				<verticalcontainer>
					<groupcontainer label="Connection Status / Signalisation Pin Status">
						<gridcontainer columns="3">
							<horizontalcontainer>
								<math visible="false" define="LED.USB" formula="(USB.ERROR_CODE==0x11)?1:0"/>
								<led data="LED.USB" bitmask="0x01" oncolor="green" offcolor="red" size="25;25"/>
								<text label="uIO Stick connected"/>
							</horizontalcontainer>
							<horizontalcontainer>
								<math visible="false" define="LED.TARGET" formula="(CTRL.FAM_PROD_STAT==0)?0:1"/>
								<led data="LED.TARGET" bitmask="0x01" oncolor="green" offcolor="red" size="25;25"/>
								<text label="Target IC accessable"/>
							</horizontalcontainer>
							<horizontalcontainer sizepolicy="fixed;fixed">
								<text label="uIO Fimware Version:"/>
								<math  formula="UIO.VERSION"/>
								<text label="."/>
								<math formula="UIO.MAINVERSION"/>
								<text label="." />
								<math formula="UIO.SUBVERSION"/>
							</horizontalcontainer>
							<horizontalcontainer>
								<led data="LED.GPIO" bitmask="0x01" oncolor="grey" offcolor="red" size="25;25"/>
								<text label="RO Pin activated"/>
							</horizontalcontainer>
							<horizontalcontainer>
								<led data="LED.GPIO" bitmask="0x08" oncolor="grey" offcolor="red" size="25;25"/>
								<text label="INT Pin activated"/>
							</horizontalcontainer>
							<horizontalcontainer>
								<led data="LED.GPIO" bitmask="0x02" oncolor="red" size="25;25"/>
								<text label="FO1 Pin activated"/>
							</horizontalcontainer>
						</gridcontainer>
					</groupcontainer>
					<groupcontainer label="Control Function">
							<gridcontainer columns="3">
								<verticalcontainer>
									<groupcontainer label="Mode">
										<verticalcontainer color="240;240;240">
											<horizontalcontainer>
												<button label="NORMAL" size="55;25">
													<action event="clicked" cmd="setDef" data="CTRL.M_S_CTRL[7:6]=0"/>
												</button>
												<button label="SLEEP" size="55;25">
													<action event="clicked" cmd="setDef" data="CTRL.M_S_CTRL[7:6]=1"/><!-- enter sleep mode -->
													<!-- Next instruction prevents device from entering sleep mode again when accessing M_S_CTRL register -->
													<action event="clicked" cmd="setDef" data="CTRL.M_S_CTRL[7:6]=0"/>
													<!-- Following instructions write restart values, which are valid after exit from sleep -->
													<action event="clicked" cmd="setDef" data="CTRL.M_S_CTRL[4:3]=0"/>
													<action event="clicked" cmd="setDef" data="CTRL.HW_CTRL[5]=0"/>
													<action event="clicked" cmd="setDef" data="CTRL.WD_CTRL[5]=0"/>
												</button>
												<button label="STOP" size="55;25">
													<action event="clicked" cmd="setDef" data="CTRL.M_S_CTRL[7:6]=2"/>
												</button>
												<button label="Soft Reset" size="65;25">
													<action event="clicked" cmd="setDef" data="CTRL.M_S_CTRL[7:6]=3"/><!-- triggers a software reset -->
													<action event="clicked" cmd="resetGUI"/>
												</button>
											</horizontalcontainer>
											<!--In order to detect the SLEEP mode the result of CTRL.FAM_PROD_STAT is used, because in SLEEP mode and NORMAL mode CTRL.M_S_CTRL is 0 -->
											<math visible="false" define="LED.MODE" formula="((CTRL.M_S_CTRL.*0xC0)==0x00)*CTRL.FAM_PROD_STAT?1:((CTRL.FAM_PROD_STAT==0)?2:((CTRL.M_S_CTRL.*0xC0)==0x80?4:8))"/>
											<math visible="false" define="LED.PRODUCT" formula="((CTRL.FAM_PROD_STAT.*0xFE)==0x24)?1:((CTRL.FAM_PROD_STAT.*0xFE)==0x28)?2:((CTRL.FAM_PROD_STAT.*0xFE)==0x2C)?4:0"/>
											<math visible="false" define="LED.VOLTAGE" formula="(CTRL.FAM_PROD_STAT.*0x1)?1:2"/>
											<gridcontainer columns="4">
												<led data="LED.MODE" bitmask="0x01" oncolor="green" size="17;17"/>
												<text label="Normal         "/>
												<led data="LED.PRODUCT" bitmask="0x01" oncolor="green" size="17;17"/>
												<text label="TLE9271"/>
												<led data="LED.MODE" bitmask="0x02" oncolor="green" size="17;17"/>
												<text label="Sleep / FS"/>
												<led data="LED.PRODUCT" bitmask="0x02" oncolor="green" size="17;17"/>
												<text label="TLE9272"/>
												<led data="LED.MODE" bitmask="0x04" oncolor="green" size="17;17"/>
												<text label="Stop"/>
												<led data="LED.PRODUCT" bitmask="0x04" oncolor="green" size="17;17"/>
												<text label="TLE9273"/>
												<led data="LED.MODE" bitmask="0x08" oncolor="green" size="17;17"/>
												<text label="Soft Reset"/>
												<led data="LED.VOLTAGE" bitmask="0x01" oncolor="green" size="17;17"/>
												<horizontalcontainer>
													<text label="3.3V "/>
													<led data="LED.VOLTAGE" bitmask="0x02" oncolor="green" size="17;17"/>
													<text label="5V"/>
												</horizontalcontainer>
											</gridcontainer>
										</verticalcontainer>
									</groupcontainer>
								</verticalcontainer>
								<verticalcontainer>
									<groupcontainer label="BOOST">
										<verticalcontainer color="240;240;240">
											<horizontalcontainer>
												<checkbox label="BOOST" define="CTRL.HW_CTRL[1]"/>
												<combo define="CTRL.HW_CTRL[2]">
													<item label="8.0 V"/>
													<item label="6.65 V"/>
												</combo>
											</horizontalcontainer>
										</verticalcontainer>
									</groupcontainer>
									<groupcontainer label="VCC2">
										<verticalcontainer color="240;240;240">
											<combo define="CTRL.M_S_CTRL[4:3]">
												<item label="VCC2 off"/>
												<item label="VCC2 on in Normal Mode"/>
												<item label="VCC2 on in Nor. + Stop M."/>
												<item label="VCC2 always on"/>
											</combo>
										</verticalcontainer>
									</groupcontainer>
								</verticalcontainer>
								<verticalcontainer>
									<groupcontainer label="VCC1">
										<verticalcontainer color="240;240;240">
											<checkbox label="OV Reset active" define="CTRL.M_S_CTRL[2]"/>
											<horizontalcontainer>
												<text label="UV Thresh."/>
												<combo define="CTRL.M_S_CTRL[1:0]">
													<item label="VRT1"/>
													<item label="VRT2"/>
													<item label="VRT3"/>
													<item label="VRT4"/>
												</combo>
											</horizontalcontainer>	
											<checkbox label="PWM by WK" define="CTRL.HW_CTRL[4]"/>
											<checkbox label="Auto PFM-PWM" define="CTRL.HW_CTRL[3]"/>		
											<text/>
										</verticalcontainer>
									</groupcontainer>
								</verticalcontainer>
								<verticalcontainer>
									<groupcontainer label="BUS Configuration">
										<verticalcontainer color="240;240;240">
											<horizontalcontainer>
												<text label="CAN"/>
												<combo define="CTRL.BUS_CTRL_1[1:0]">
													<item label="OFF"/>
													<item label="Wake capable"/>
													<item label="Receive only"/>
													<item label="Normal"/>
												</combo>
											</horizontalcontainer>	
											<horizontalcontainer>
												<text label="LIN1"/>
												<combo define="CTRL.BUS_CTRL_1[4:3]">
													<item label="OFF"/>
													<item label="Wake capable"/>
													<item label="Receive only"/>
													<item label="Normal"/>
												</combo>
											</horizontalcontainer>	
											<horizontalcontainer>
												<text label="LIN2"/>
												<combo define="CTRL.BUS_CTRL_2[1:0]">
													<item label="OFF"/>
													<item label="Wake capable"/>
													<item label="Receive only"/>
													<item label="Normal"/>
												</combo>
											</horizontalcontainer>
											<horizontalcontainer>
												<text label="LIN3"/>
												<combo define="CTRL.BUS_CTRL_2[4:3]">
													<item label="OFF"/>
													<item label="Wake capable"/>
													<item label="Receive only"/>
													<item label="Normal"/>
												</combo>
											</horizontalcontainer>
											<horizontalcontainer>
												<text label="LIN4"/>
												<combo define="CTRL.BUS_CTRL_2[7:6]">
													<item label="OFF"/>
													<item label="Wake capable"/>
													<item label="Receive only"/>
													<item label="Normal"/>
												</combo>
											</horizontalcontainer>
											<checkbox label="LIN TXD Time-Out" define="CTRL.BUS_CTRL_1[5]" default="1"/>
											<checkbox label="LIN Low-Slope" define="CTRL.BUS_CTRL_1[6]"/>
											<checkbox label="LIN Slope control" role="inverted" define="CTRL.BUS_CTRL_1[7]"/>
										</verticalcontainer>
									</groupcontainer>
								</verticalcontainer>
								<verticalcontainer>
									<groupcontainer label="Wake-up (WK)">
										<verticalcontainer color="240;240;240">
											<checkbox label="Enable WK pin" define="CTRL.WK_CTRL_2[0]" default="1"/>
											<horizontalcontainer>
												<text label="Pull Device"/>
												<combo define="CTRL.WK_PUPD_CTRL[1:0]">
													<item label="None"/>
													<item label="Pull-down"/>
													<item label="Pull-up"/>
													<item label="Automatic"/>
												</combo>
											</horizontalcontainer>
											<checkbox label="Enable WK Timer" define="CTRL.WK_CTRL_1[6]"/>
											<horizontalcontainer>
												<text label="WK Timer Period"/>
												<combo define="CTRL.TIMER1_CTRL[2:0]">
													<item label="10 ms"/>
													<item label="20 ms"/>
													<item label="50 ms"/>
													<item label="100 ms"/>
													<item label="200 ms"/>
													<item label="1 s"/>
													<item label="2 s"/>
												</combo>
											</horizontalcontainer>
										</verticalcontainer>
									</groupcontainer>
									<groupcontainer label="GPIOs and other pins">
										<verticalcontainer color="240;240;240">	
											<checkbox label="CFG" define="CTRL.HW_CTRL[0]"/>
											<checkbox label="FOx_EN" define="CTRL.HW_CTRL[5]"/>
											<checkbox label="FSI disabled (FO2 active)" define="CTRL.HW_CTRL[7]"/>
											<horizontalcontainer>
												<text label="PWM Lag Time"/>
												<combo  define="CTRL.HW_CTRL[6]">
													<item label="100 us"/>
													<item label="1 ms"/>
												</combo>
											</horizontalcontainer>	

										</verticalcontainer>
									</groupcontainer>
								</verticalcontainer>
								<verticalcontainer>
									<groupcontainer label="Watchdog">
										<verticalcontainer color="240;240;240">
											<radio define="CTRL.WD_CTRL[5]">
												<radiobutton label="Time-out Watchdog"/>
												<radiobutton label="Windows Watchdog"/>
											</radio>
											<checkbox label="Starts WD after CAN Wake" define="CTRL.WD_CTRL[4]" default="1"/>
											<horizontalline size="-1;7"/>
											<text label="After 3 consecutive WD fails:"/>
											<radio define="CTRL.WD_CTRL[3]">
												<radiobutton label="Continue reset generation"/>
												<radiobutton label="Stop reset generation"/>
											</radio>
											<togglebutton define="WDT_STOP" label="Stop WDT Trigger">
												<action event="unchecked" cmd="sendUSB" data="0x06;0x10;0x01;0x00;0xB4"/><!-- Activate SBC Watchdog trigger -->
												<action event="checked" cmd="sendUSB" data="0x06;0x10;0x00;0x00;0xB4"/><!-- Deactivate SBC Watchdog trigger -->
											</togglebutton>
											<text/>
											<text/>											
										</verticalcontainer>
									</groupcontainer>
								</verticalcontainer>
							</gridcontainer>
					</groupcontainer>
				</verticalcontainer>
				<verticalcontainer>
					<groupcontainer label="Status">
						<verticalcontainer>
							<horizontalcontainer>
								<groupcontainer label="Thermal Status">
									<gridcontainer columns="2">
										<led data="CTRL.THERM_STAT" bitmask="0x04" oncolor="green" size="15;15"/>
										<text label="TSD2"/>
										<led data="CTRL.THERM_STAT" bitmask="0x02" oncolor="green" size="15;15"/>
										<text label="TSD1"/>
										<led data="CTRL.THERM_STAT" bitmask="0x01" oncolor="green" size="15;15"/>
										<text label="TPW"/>
										<text/>
										<text/>
										<text/>
										<button label="CLEAR" size="45;25">
											<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC2"/>
											<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x42" recdata="USB.ERROR_CODE;?;CTRL.THERM_STAT"/>
										</button>
									</gridcontainer>
								</groupcontainer>
								<groupcontainer label="Supply Status 1">
									<gridcontainer columns="2">
										<led data="CTRL.SUP_STAT" bitmask="0x80" oncolor="green" size="15;15"/>
										<text label="POR"/>
										<led data="CTRL.SUP_STAT" bitmask="0x40" oncolor="green" size="15;15"/>
										<text label="VLIN UV"/>
										<led data="CTRL.SUP_STAT" bitmask="0x20" oncolor="green" size="15;15"/>
										<text label="VCC1 OV"/>
										<led data="CTRL.SUP_STAT" bitmask="0x10" oncolor="green" size="15;15"/>
										<text label="VCC2 OT"/>
										<led data="CTRL.SUP_STAT" bitmask="0x08" oncolor="green" size="15;15"/>
										<text label="VCC2 UV"/>
										<led data="CTRL.SUP_STAT" bitmask="0x04" oncolor="green" size="15;15"/>
										<text label="VCC1 SC"/>
										<led data="CTRL.SUP_STAT" bitmask="0x01" oncolor="green" size="15;15"/>
										<text label="VCC1 UV"/>
										<text/>
										<text/>
										<text/>
										<button label="CLEAR" size="45;25">
											<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC1"/>
											<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x41" recdata="USB.ERROR_CODE;?;CTRL.SUP_STAT"/>
										</button>
									</gridcontainer>
								</groupcontainer>
								<groupcontainer label="Bus Status 1">
									<gridcontainer columns="2">
										<led data="CTRL.BUS_STAT_1" bitmask="0x40" oncolor="green" size="15;15"/>
										<text label="LIN1 FAIL1"/>
										<led data="CTRL.BUS_STAT_1" bitmask="0x20" oncolor="green" size="15;15"/>
										<text label="LIN1 FAIL0"/>
										<led data="CTRL.BUS_STAT_1" bitmask="0x04" oncolor="green" size="15;15"/>
										<text label="CAN FAIL1"/>
										<led data="CTRL.BUS_STAT_1" bitmask="0x02" oncolor="green" size="15;15"/>
										<text label="CAN FAIL0"/>
										<led data="CTRL.BUS_STAT_1" bitmask="0x01" oncolor="green" size="15;15"/>
										<text label="VCAN UV"/>
										<text/>
										<text/>
										<text/>
										<button label="CLEAR" size="45;25">
											<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC4"/>
											<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x44" recdata="USB.ERROR_CODE;?;CTRL.BUS_STAT_1"/>
										</button>
									</gridcontainer>
								</groupcontainer>
								<groupcontainer label="Bus Status 2 ">
									<gridcontainer columns="2">
										<led data="CTRL.BUS_STAT_2" bitmask="0x40" oncolor="green" size="15;15"/>
										<text label="LIN4 FAIL1"/>
										<led data="CTRL.BUS_STAT_2" bitmask="0x20" oncolor="green" size="15;15"/>
										<text label="LIN4 FAIL0"/>
										<led data="CTRL.BUS_STAT_2" bitmask="0x10" oncolor="green" size="15;15"/>
										<text label="LIN3 FAIL1"/>
										<led data="CTRL.BUS_STAT_2" bitmask="0x08" oncolor="green" size="15;15"/>
										<text label="LIN3 FAIL0"/>
										<led data="CTRL.BUS_STAT_2" bitmask="0x04" oncolor="green" size="15;15"/>
										<text label="LIN2 FAIL1"/>
										<led data="CTRL.BUS_STAT_2" bitmask="0x02" oncolor="green" size="15;15"/>
										<text label="LIN2 FAIL0"/>
										<text/>
										<text/>										
										<text/>
										<button label="CLEAR" size="45;25">
											<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC5"/>
											<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x45" recdata="USB.ERROR_CODE;?;CTRL.BUS_STAT_2"/>
										</button>
									</gridcontainer>
								</groupcontainer>
							</horizontalcontainer>
							<horizontalcontainer>
								<groupcontainer label="Device Status">
									<gridcontainer columns="2">
										<led data="CTRL.DEV_STAT" bitmask="0x80" oncolor="green" size="15;15"/>
										<text label="DEV STAT1"/>
										<led data="CTRL.DEV_STAT" bitmask="0x40" oncolor="green" size="15;15"/>
										<text label="DEV STAT0"/>
										<led data="CTRL.DEV_STAT" bitmask="0x20" oncolor="green" size="15;15"/>
										<text label="RO CL HIGH"/>
										<led data="CTRL.DEV_STAT" bitmask="0x10" oncolor="green" size="15;15"/>
										<text label="FSI FAIL"/>
										<led data="CTRL.DEV_STAT" bitmask="0x02" oncolor="green" size="15;15"/>
										<text label="SPI FAIL"/>
										<led data="CTRL.DEV_STAT" bitmask="0x01" oncolor="green" size="15;15"/>
										<text label="FAILURE"/>
										<text/>
										<button label="CLEAR" size="45;25">
											<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC3"/>
											<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x43" recdata="USB.ERROR_CODE;?;CTRL.DEV_STAT"/>
										</button>
										<text/>
										<text/>
										<led data="CTRL.DEV_STAT" bitmask="0x08" oncolor="green" size="15;15"/>
										<text label="WD FAIL1"/>
										<led data="CTRL.DEV_STAT" bitmask="0x04" oncolor="green" size="15;15"/>
										<text label="WD FAIL0"/>
										<text/>
										<text/>
									</gridcontainer>
								</groupcontainer>
								<groupcontainer label="Wake Level Status">
									<gridcontainer columns="2">
										<led data="CTRL.WK_LVL_STAT" bitmask="0x80" oncolor="green" size="15;15"/>
										<text label="TEST"/>
										<led data="CTRL.WK_LVL_STAT" bitmask="0x20" oncolor="green" size="15;15"/>
										<text label="CFG2"/>
										<led data="CTRL.WK_LVL_STAT" bitmask="0x01" oncolor="green" size="15;15"/>
										<text label="WK"/>
										<text/>
										<text/>
									</gridcontainer>
								</groupcontainer>
								<groupcontainer label="Wake Status 1 + 2">
									<gridcontainer columns="2">
										<led data="CTRL.WK_STAT_1" bitmask="0x80" oncolor="green" size="15;15"/>
										<text label="PFM"/>
										<led data="CTRL.WK_STAT_1" bitmask="0x20" oncolor="green" size="15;15"/>
										<text label="CAN"/>
										<led data="CTRL.WK_STAT_1" bitmask="0x10" oncolor="green" size="15;15"/>
										<text label="TIMER"/>
										<led data="CTRL.WK_STAT_1" bitmask="0x01" oncolor="green" size="15;15"/>
										<text label="WK"/>
										<text/>
										<button label="CLEAR" size="45;25">
											<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC6"/>
											<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x46" recdata="USB.ERROR_CODE;?;CTRL.WK_STAT_1;?"/>
										</button>
										<led data="CTRL.WK_STAT_2" bitmask="0x08" oncolor="green" size="15;15"/>
										<text label="LIN4"/>
										<led data="CTRL.WK_STAT_2" bitmask="0x04" oncolor="green" size="15;15"/>
										<text label="LIN3"/>
										<led data="CTRL.WK_STAT_2" bitmask="0x02" oncolor="green" size="15;15"/>
										<text label="LIN2"/>
										<led data="CTRL.WK_STAT_2" bitmask="0x01" oncolor="green" size="15;15"/>
										<text label="LIN1"/>
										<text/>
										<button label="CLEAR" size="45;25">
											<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC7"/>
											<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x47" recdata="USB.ERROR_CODE;?;CTRL.WK_STAT_2"/>
										</button>
										<text/>
									</gridcontainer>
								</groupcontainer>
								<groupcontainer label="SMPS Status">
									<gridcontainer columns="2">
										<led data="CTRL.SMPS_STAT" bitmask="0x80" oncolor="green" size="15;15"/>
										<text label="BST ACT"/>
										<led data="CTRL.SMPS_STAT" bitmask="0x40" oncolor="green" size="15;15"/>
										<text label="BST SH"/>
										<led data="CTRL.SMPS_STAT" bitmask="0x20" oncolor="green" size="15;15"/>
										<text label="BST OP"/>
										<led data="CTRL.SMPS_STAT" bitmask="0x10" oncolor="green" size="15;15"/>
										<text label="BST GSH"/>
										<led data="CTRL.SMPS_STAT" bitmask="0x04" oncolor="green" size="15;15"/>
										<text label="BCK SH"/>
										<led data="CTRL.SMPS_STAT" bitmask="0x02" oncolor="green" size="15;15"/>
										<text label="BCK OP"/>
										<led data="CTRL.SMPS_STAT" bitmask="0x01" oncolor="green" size="15;15"/>
										<text label="BCK OOR"/>
										<text/>
										<text/>
									</gridcontainer>
								</groupcontainer>
							</horizontalcontainer>
							<horizontalcontainer>
								<button label="CLEAR DIAGNOSTIC STATUS">
									<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC1"/><!-- clear SUP_STAT-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x41" recdata="?;?;CTRL.SUP_STAT"/><!-- read SUP_STAT-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC2"/><!-- clear THERM_STAT-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x42" recdata="?;?;CTRL.THERM_STAT"/><!-- read THERM_STAT-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC3"/><!-- clear DEV_STAT-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x43" recdata="?;?;CTRL.DEV_STAT"/><!-- read DEV_STAT-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC4"/><!-- clear BUS_STAT_1-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x44" recdata="?;?;CTRL.BUS_STAT_1"/><!-- read BUS_STAT_1-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC5"/><!-- clear BUS_STAT_2-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x45" recdata="?;?;CTRL.BUS_STAT_2"/><!-- read BUS_STAT_2-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC6"/><!-- clear WK_STAT_1-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x46" recdata="?;?;CTRL.WK_STAT_1"/><!-- read WK_STAT_1-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC7"/><!-- clear WK_STAT_2-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x47" recdata="?;?;CTRL.WK_STAT_2"/><!-- read WK_STAT_2-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC8"/><!-- clear WK_LVL_STAT-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x48" recdata="?;?;CTRL.WK_LVL_STAT"/><!-- read WK_LVL_STAT-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xCC"/><!-- clear SMPS_STAT-->
									<action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x4C" recdata="?;?;CTRL.SMPS_STAT"/><!-- read SMPS_STAT-->
								</button>
								<togglebutton define="STATUS_REG_READ_STOP" label="STOP PERIODICAL READ OF STATUS REGISTER">
									<action event="checked" cmd="setDef" data="TIMER_STATUS_READ.run=0"/>
									<action event="unchecked" cmd="setDef" data="TIMER_STATUS_READ.run=1"/>
								</togglebutton>
							</horizontalcontainer>
						</verticalcontainer>
					</groupcontainer>
				</verticalcontainer>
			</gridcontainer>
		</header>
	</verticalcontainer>
</ifxmlcfg>
