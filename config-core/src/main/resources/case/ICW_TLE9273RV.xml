<?xml version="1.0" encoding="UTF-8"?>
<ifxmlcfg version="2.0.15">
	<checksum>f07f2dabda77f66822c2e1c7ea69a42a132cd2194d712f8356466f9f95c6f4aa</checksum>
	<version label="V1.0.1"/>
	<fwversion min="2.2.1" max="*.*.*"/>
	<svd file="ICW_TLE9273/TLE9273.svd"/>
	<loadsaveallowed value="0"/>
	<verticalcontainer label="DCDC-SBC Register Compiler" color="255;255;255;255" sizepolicy="fixed;fixed">
		<var define="CTRL.FAM_PROD_STAT"/><!-- Required for display of product type and mode -->
		<var define="CTRL.SYS_DIAG1" svd="0"/>
		<var define="USB.ERROR_CODE" svd="0"/>
		<var define="LED.GPIO"/>
		<timer visible="false" define="INIT" svd="0" interval="1" singleShot="1" run="1">
			<action event="changed" cmd="sendUSB" data="0x01;0x12;0;0x55;0xAA"/>		<!-- Switch VS (12V) off -->
			<action event="changed" cmd="sendUSB" data="0x01;0x13;0x55;0xAA"/>			<!-- System Reset -->
			<action event="changed" cmd="sendUSB" data="0x02;0x41;0x0a"/>				<!-- SPI Baudrate 1 MHz -->
			<action event="changed" cmd="sendUSB" data="0x02;0x42"/>					<!-- SPI: passive clock level is low, receive on rising edge -->
			<action event="changed" cmd="sendUSB" data="0x02;0x46"/>					<!-- SPI: send LSB first -->
			<action event="changed" cmd="sendUSB" data="0x02;0x48;0x10"/>				<!-- SPI: word length 16 bit -->
			<action event="changed" cmd="sendUSB" data="0x06;0x10;0x01;0x00;0xB4"/>		<!-- Set SBC Watchdog trigger to 180ms -->
			<action event="changed" cmd="sendUSB" data="0x04;0x36;0;20"/>				<!-- Set GPIO Read&Keep for GPIO0 with 20*100ms hold time-->
			<action event="changed" cmd="sendUSB" data="0x04;0x36;1;20"/>				<!-- Set GPIO Read&Keep for GPIO1 with 20*100ms hold time -->
			<action event="changed" cmd="sendUSB" data="0x04;0x36;3;20"/>				<!-- Set GPIO Read&Keep for GPIO3 with 20*100ms hold time -->
		</timer>
		<timer visible="false" define="TIMER_M_S_CTRL" svd="0" interval="500" singleShot="1" run="1">
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x7E" recdata="?;?;CTRL.FAM_PROD_STAT"/>
		</timer>
		<timer visible="false" define="TIMER_GPIO" svd="0" interval="500" singleShot="0" run="1">
			<action event="changed" cmd="sendUSB" data="0x04;0x38" recdata="USB.ERROR_CODE;?;LED.GPIO"/><!-- read all GPIO pins -->
		</timer>
		<groupcontainer label="Connection Status / Signalisation Pin Status">
			<gridcontainer columns="5">
				<horizontalcontainer>
					<math visible="false" define="LED.USB" svd="0" formula="(USB.ERROR_CODE==0x11)?1:0"/>
					<led data="LED.USB" bitmask="0x01" oncolor="green" offcolor="red" size="22;22"/>
					<text label="uIO Stick connected"/>
				</horizontalcontainer>
				<horizontalcontainer>
					<math visible="false" define="LED.TARGET" svd="0" formula="(CTRL.FAM_PROD_STAT==0)?0:1"/>
					<led data="LED.TARGET" bitmask="0x01" oncolor="green" offcolor="red" size="22;22"/>
					<text label="Target IC accessable"/>
				</horizontalcontainer>
				<horizontalcontainer>
					<led data="LED.GPIO" bitmask="0x01" oncolor="grey" offcolor="red" size="22;22"/>
					<text label="RSTN Pin activated"/>
				</horizontalcontainer>
				<horizontalcontainer>
					<led data="LED.GPIO" bitmask="0x08" oncolor="grey" offcolor="red" size="22;22"/>
					<text label="INTN Pin activated"/>
				</horizontalcontainer>
				<horizontalcontainer>
					<led data="LED.GPIO" bitmask="0x02" oncolor="red" size="22;22"/>
					<text label="FO Pin activated"/>
				</horizontalcontainer>
			</gridcontainer>
		</groupcontainer>
		<groupcontainer label="1. Register Selection">
			<verticalcontainer>
				<horizontalcontainer>
					<text label="Register Domain Selection:"/>
					<combo define="$PERIPHERALS$" svd="0" setdef="REGISTER_COMBO.register=$PERIPHERALS$" default="label=CTRL"/>
					<text label="Register Selection:"/>
					<combo define="REGISTER_COMBO" svd="0" setdef="REGVIEW1.module=REGISTER_COMBO.module;REGVIEW1.register=REGISTER_COMBO" default="register=$PERIPHERALS$"/>
					<verticalcontainer>
						<radio define="REGISTER.READ_WRITE_CLEAR_SEL" svd="0" default="1">
							<radiobutton label="Read" setdef="TABLE.func=0"/>
							<radiobutton label="Write/Clear" setdef="TABLE.func=1"/>
						</radio>
					</verticalcontainer>
				</horizontalcontainer>
                <registerview label="regview" bits="8" numrows="2" define="REGVIEW1" svd="0" sizepolicy="preferred;fixed"/>
			</verticalcontainer>
		</groupcontainer>
		<groupcontainer label="2. Register Compilation">
			<verticalcontainer>
				<gridcontainer columns="3">
					<verticalcontainer>
						<button label="--&gt;" define="UI.COPY_TO_LIST" svd="0">
							<action event="clicked" cmd="setDef" data="TABLE.value=REGVIEW1.value"/>
							<action event="clicked" cmd="setDef" data="TABLE.address=REGVIEW1.address"/>
							<action event="clicked" cmd="setDef" data="TABLE.registername=REGVIEW1.registername"/>
						</button>
						<button label="&lt;--" define="UI.COPY_FROM_LIST" svd="0">
							<action event="clicked" cmd="setDef" data="$PERIPHERALS$.label=TABLE.module"/>
							<action event="clicked" cmd="setDef" data="REGISTER_COMBO.register=$PERIPHERALS$"/>
							<action event="clicked" cmd="setDef" data="REGISTER_COMBO.label=TABLE.registername"/>
							<action event="clicked" cmd="setDef" data="REGVIEW1.value=TABLE.value"/>
							<action event="clicked" cmd="setDef" data="REGVIEW1.module=TABLE.module"/>
							<action event="clicked" cmd="setDef" data="REGVIEW1.register=TABLE.registername"/>
						</button>
					</verticalcontainer>
					<math define="ADDRESS" svd="0" formula="$ADDRESS$ + $FUNC$*0x80" visible="false" />
					<table label="regtable" define="TABLE" svd="0" numrows="256" sendformat ="02;0x4C;02;1:$VALUE$;1:ADDRESS" recformat="?;?;0x%02x;0x%02x" tooltiprecformat="0;0">
						<column label="Register"   tooltip="Register Name"                 type="regname"/>
						<column label="Address"    tooltip="Address of register [hex]"     type="address" format="0x%02x"/>
						<column label="W(1)/R(0)"  tooltip="Write/Clear=1 Read=0"          type="func"    format="%d" default="1"/>
						<column label="Value"      tooltip="Value to write in Register"    type="value"   format="0x%02x"/>
						<column label="Delay [ms]" tooltip="Delay after sending in [msec]" type="delay"   format="%d"/>
						<column label="Answer"     tooltip="Answer of device"              type="answer"/>
						<column label="Comment"    tooltip="Comment of this line"          type="comment"/>
					</table>
					<verticalcontainer>
						<groupcontainer label="Edit">
							<verticalcontainer>
								<checkbox label="Overwrite line" define="INSERT_MODE" svd="0" default="1">
									<action event="unchecked" cmd="setDef" data="TABLE.insertmode=1"/>
									<action event="checked" cmd="setDef" data="TABLE.insertmode=0"/>
								</checkbox>
								<button label="Delete Line" define="DELETE_LINE" svd="0">
									<action event="clicked" cmd="setDef" data="TABLE.delete=1"/>
								</button>
								<button label="Delete All" define="DELETE_ALL" svd="0">
									<action event="clicked" cmd="setDef" data="TABLE.deleteall=1"/>
								</button>
							</verticalcontainer>
						</groupcontainer>
						<groupcontainer label="USB">
							<verticalcontainer>
								<button label="--> SEND" define="SEND_USB" svd="0">
									<action event="clicked" cmd="setDef" data="TABLE.sendusb=1"/>
								</button>
								<checkbox label="Single-Step" define="SINGLE_STEP" svd="0">
									<action event="checked" cmd="setDef" data="TABLE.singlestep=1"/>
									<action event="unchecked" cmd="setDef" data="TABLE.singlestep=0"/>
								</checkbox>
								<horizontalcontainer>
									<text label="Loop count"></text>
									<spinbox label="Loop" define="TABLE_LOOP" svd="0" default="1">
										<action event="changed" cmd="setDef" data="TABLE.sendloop=TABLE_LOOP"/>
									</spinbox>
								</horizontalcontainer>
								<horizontalline size="-1;1"/>
								<togglebutton define="WDT_STOP" label="Stop WDT Trigger" svd="0">
									<action event="unchecked" cmd="sendUSB" data="0x06;0x10;0x01;0x00;0xB4"/><!-- Activate SBC Watchdog trigger (180ms)-->
									<action event="checked" cmd="sendUSB" data="0x06;0x10;0x00;0x00;0xB4"/><!-- Deactivate SBC Watchdog trigger -->
								</togglebutton>
							</verticalcontainer>
						</groupcontainer>
						<groupcontainer label="Data">
							<verticalcontainer>
								<button label="Load" define="LOAD" svd="0">
									<action event="clicked" cmd="setDef" data="TABLE.load=1"/>
								</button>
								<button label="Partial Load" define="PARTIAL_LOAD" svd="0">
									<action event="clicked" cmd="setDef" data="TABLE.loadat=1"/>
								</button>
								<button label="Save" define="SAVE" svd="0">
									<action event="clicked" cmd="setDef" data="TABLE.save=1"/>
								</button>
								<button label="Export" define="EXPORT" svd="0">
									<action event="clicked" cmd="setDef" data="TABLE.export=1"/>
								</button>
							</verticalcontainer>
						</groupcontainer>
					</verticalcontainer>
				</gridcontainer>
			</verticalcontainer>
		</groupcontainer>
	</verticalcontainer>
</ifxmlcfg>
