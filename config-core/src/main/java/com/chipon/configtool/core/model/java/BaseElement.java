package com.chipon.configtool.core.model.java;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

/**
 * Base class for all XML elements with common attributes.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class BaseElement {
    
    @JacksonXmlProperty(isAttribute = true)
    private Boolean visible;
    
    @JacksonXmlProperty(isAttribute = true)
    private String define;
    
    // Default constructor
    public BaseElement() {}
    
    // Constructor with parameters
    public BaseElement(Boolean visible, String define) {
        this.visible = visible;
        this.define = define;
    }
    
    // Getters and setters
    public Boolean getVisible() {
        return visible;
    }
    
    public void setVisible(Boolean visible) {
        this.visible = visible;
    }
    
    public String getDefine() {
        return define;
    }
    
    public void setDefine(String define) {
        this.define = define;
    }
    
    @Override
    public String toString() {
        return getClass().getSimpleName() + "(define=" + define + ", visible=" + visible + ")";
    }
}
