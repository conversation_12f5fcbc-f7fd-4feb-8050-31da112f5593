package com.chipon.configtool.core.model.container

import com.chipon.configtool.core.model.BaseContainer
import com.chipon.configtool.core.model.container.GroupContainer
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Represents a tab container element.
 * Maps to <tabcontainer> elements in the XML.
 */
class TabContainer : BaseContainer() {
    @JacksonXmlProperty(isAttribute = true)
    override var visible: Boolean? = null

    @JacksonXmlProperty(isAttribute = true)
    override var color: String? = null

    @JacksonXmlProperty(isAttribute = true)
    override var sizepolicy: String? = null

    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null

    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var verticalContainers: List<VerticalContainer> = emptyList()

    @JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var horizontalContainers: List<HorizontalContainer> = emptyList()

    @JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var gridContainers: List<GridContainer> = emptyList()

    @JacksonXmlProperty(localName = "groupcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var groupContainers: List<GroupContainer> = emptyList()
}
