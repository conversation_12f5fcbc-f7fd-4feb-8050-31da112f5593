package com.chipon.configtool.core.model.xml

import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import java.io.File

/**
 * Simple test runner to validate our XML models.
 * This can be run as a main function to test the models.
 */
fun main() {
    println("Starting XML Model Validation...")
    
    val xmlMapper = XmlMapper().apply {
        registerKotlinModule()
    }
    
    // Test with a simple XML file first
    val caseDir = File("config-core/src/main/resources/case")
    if (!caseDir.exists()) {
        println("ERROR: Case directory not found at ${caseDir.absolutePath}")
        return
    }
    
    val xmlFiles = caseDir.listFiles { _, name -> name.endsWith(".xml") }
    if (xmlFiles == null || xmlFiles.isEmpty()) {
        println("ERROR: No XML files found in case directory")
        return
    }
    
    println("Found ${xmlFiles.size} XML files to test")
    
    var successCount = 0
    var failureCount = 0
    
    xmlFiles.forEach { xmlFile ->
        print("Testing ${xmlFile.name}... ")
        
        try {
            val ifxmlcfg = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
            
            // Basic validation
            if (ifxmlcfg.version != null && ifxmlcfg.versionInfo != null) {
                println("SUCCESS")
                successCount++
                
                // Print some details
                println("  Version: ${ifxmlcfg.version}")
                println("  Version Info: ${ifxmlcfg.versionInfo?.label}")
                println("  FW Version: ${ifxmlcfg.fwVersion?.min} - ${ifxmlcfg.fwVersion?.max}")
                
                ifxmlcfg.verticalContainers?.let { containers ->
                    println("  Vertical Containers: ${containers.size}")
                    containers.forEach { container ->
                        container.variables?.let { vars ->
                            println("    Variables: ${vars.size}")
                        }
                        container.timers?.let { timers ->
                            println("    Timers: ${timers.size}")
                        }
                        container.mathElements?.let { math ->
                            println("    Math Elements: ${math.size}")
                        }
                    }
                }
                
            } else {
                println("PARTIAL SUCCESS (missing some required fields)")
                successCount++
            }
            
        } catch (e: Exception) {
            println("FAILED")
            println("  Error: ${e.message}")
            failureCount++
            
            // Print first few lines of stack trace for debugging
            e.stackTrace.take(3).forEach { 
                println("    at ${it}")
            }
        }
        
        println()
    }
    
    println("=== SUMMARY ===")
    println("Total files: ${xmlFiles.size}")
    println("Successful: $successCount")
    println("Failed: $failureCount")
    
    if (failureCount == 0) {
        println("🎉 All XML files parsed successfully!")
        
        // Test round-trip serialization
        println("\nTesting round-trip serialization...")
        testRoundTrip(xmlMapper, xmlFiles.first())
        
    } else {
        println("❌ Some files failed to parse. Check the errors above.")
    }
}

private fun testRoundTrip(xmlMapper: XmlMapper, xmlFile: File) {
    try {
        println("Testing round-trip with ${xmlFile.name}...")
        
        // Parse original
        val original = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
        
        // Serialize back to XML
        val serializedXml = xmlMapper.writeValueAsString(original)
        
        // Parse the serialized version
        val roundTrip = xmlMapper.readValue(serializedXml, IfxmlcfgRoot::class.java)
        
        // Compare key properties
        val issues = mutableListOf<String>()
        
        if (original.version != roundTrip.version) {
            issues.add("Version mismatch")
        }
        
        if (original.versionInfo?.label != roundTrip.versionInfo?.label) {
            issues.add("Version label mismatch")
        }
        
        if (issues.isEmpty()) {
            println("✓ Round-trip test successful!")
        } else {
            println("⚠ Round-trip test had issues: ${issues.joinToString(", ")}")
        }
        
        // Print a sample of the serialized XML
        println("\nSample of serialized XML:")
        println(serializedXml.take(500) + "...")
        
    } catch (e: Exception) {
        println("❌ Round-trip test failed: ${e.message}")
    }
}
