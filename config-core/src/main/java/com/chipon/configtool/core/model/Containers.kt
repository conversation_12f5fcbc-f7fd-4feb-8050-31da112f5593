package com.chipon.configtool.core.model

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Base class for container elements with common container attributes.
 */
abstract class BaseContainer : BaseElement() {

    @JacksonXmlProperty(isAttribute = true)
    open var label: String? = null

    var children: List<BaseElement> = mutableListOf()
}

class GroupContainer : BaseContainer()
class TabContainer : BaseContainer()
class GridContainer : BaseContainer()
class HorizontalContainer : BaseContainer()
class VerticalContainer : BaseContainer()