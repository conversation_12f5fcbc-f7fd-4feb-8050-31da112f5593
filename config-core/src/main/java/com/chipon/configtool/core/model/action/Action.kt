package com.chipon.configtool.core.model.action

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Represents an action element.
 * Maps to <action> elements in the XML.
 */
data class Action(
    @field:JacksonXmlProperty(isAttribute = true)
    val event: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val cmd: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val data: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val recdata: String? = null
)
