package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText
import com.chipon.configtool.core.model.BaseUIControl

/**
 * Represents a text UI control element.
 * Maps to <text> elements in the XML.
 */
data class Text(
    @JacksonXmlProperty(isAttribute = true)
    override var visible: Boolean? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var size: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var data: String? = null,
    
    @JacksonXmlText
    var content: String? = null
) : BaseUIControl(visible, define, label, size, data)
