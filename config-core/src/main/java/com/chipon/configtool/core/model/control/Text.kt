package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText
import com.chipon.configtool.core.model.BaseUIControl

/**
 * Represents a text UI control element.
 * Maps to <text> elements in the XML.
 */
data class Text(
    @field:JacksonXmlProperty(isAttribute = true)
    override val visible: Boolean? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val size: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val data: String? = null,
    
    @JacksonXmlText
    val content: String? = null
) : BaseUIControl(visible, define, label, size, data)
