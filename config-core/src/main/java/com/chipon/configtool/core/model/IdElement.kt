package com.chipon.configtool.core.model

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Base class for all XML elements with common attributes.
 */
abstract class IdElement {

    @JacksonXmlProperty(isAttribute = true)
    open var id: String? = null

}

/**
 * Base class for container elements with common container attributes.
 */
abstract class BaseContainer : IdElement() {

    @JacksonXmlProperty(isAttribute = true)
    open var color: String? = null

    @JacksonXmlProperty(isAttribute = true)
    open var sizepolicy: String? = null

    @JacksonXmlProperty(isAttribute = true)
    open var label: String? = null

}

/**
 * Base class for UI control elements.
 */
abstract class BaseUIControl : IdElement() {

    @JacksonXmlProperty(isAttribute = true)
    open var label: String? = null

    @JacksonXmlProperty(isAttribute = true)
    open var size: String? = null

    @JacksonXmlProperty(isAttribute = true)
    open var data: String? = null

}
