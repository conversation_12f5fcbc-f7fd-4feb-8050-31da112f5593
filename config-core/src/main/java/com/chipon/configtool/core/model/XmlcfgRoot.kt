package com.chipon.configtool.core.model

import com.chipon.configtool.core.model.container.VerticalContainer
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement

/**
 * Root element for IFX XML configuration files.
 * Represents the <ifxmlcfg> element with version attribute and child elements.
 */
@JacksonXmlRootElement(localName = "xmlcfg")
data class IfxmlcfgRoot(
    @JacksonXmlProperty(isAttribute = true)
    val version: String,
    
    @JacksonXmlProperty(localName = "checksum")
    val checksum: String? = null,
    
    @JacksonXmlProperty(localName = "version")
    val versionInfo: Version? = null,
    
    @JacksonXmlProperty(localName = "fwversion")
    val fwVersion: FwVersion? = null,
    
    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer> = emptyList()
)

/**
 * Version information element.
 */
data class Version(
    @JacksonXmlProperty(isAttribute = true)
    val label: String
)

/**
 * Firmware version element with min and max version constraints.
 */
data class FwVersion(
    @JacksonXmlProperty(isAttribute = true)
    val min: String,
    
    @JacksonXmlProperty(isAttribute = true)
    val max: String
)
