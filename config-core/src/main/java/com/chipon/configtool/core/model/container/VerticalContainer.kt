package com.chipon.configtool.core.model.container

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseContainer
import com.chipon.configtool.core.model.Variable
import com.chipon.configtool.core.model.Timer
import com.chipon.configtool.core.model.Math
import com.chipon.configtool.core.model.control.*

/**
 * Represents a vertical container element.
 * Maps to <verticalcontainer> elements in the XML.
 */
data class VerticalContainer(
    @field:JacksonXmlProperty(isAttribute = true)
    override val visible: Boolean? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val color: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val sizepolicy: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val label: String? = null,
    
    @field:JacksonXmlProperty(localName = "var")
    @JacksonXmlElementWrapper(useWrapping = false)
    val variables: List<Variable> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "timer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val timers: List<Timer> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "math")
    @JacksonXmlElementWrapper(useWrapping = false)
    val mathElements: List<Math> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "header")
    @JacksonXmlElementWrapper(useWrapping = false)
    val headers: List<Header> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "groupcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val groupContainers: List<GroupContainer> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val gridContainers: List<GridContainer> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "tabcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val tabContainers: List<TabContainer> = emptyList()
) : BaseContainer(visible, define, color, sizepolicy, label)
