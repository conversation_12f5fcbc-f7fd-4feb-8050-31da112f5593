package com.chipon.configtool.core.model.container

import com.chipon.configtool.core.model.BaseContainer
import com.chipon.configtool.core.model.Math
import com.chipon.configtool.core.model.Timer
import com.chipon.configtool.core.model.Variable
import com.chipon.configtool.core.model.container.GroupContainer
import com.chipon.configtool.core.model.control.Header
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Represents a vertical container element.
 * Maps to <verticalcontainer> elements in the XML.
 */
class VerticalContainer : BaseContainer() {

    @JacksonXmlProperty(localName = "var")
    @JacksonXmlElementWrapper(useWrapping = false)
    var variables: List<Variable> = emptyList()

    @JacksonXmlProperty(localName = "timer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var timers: List<Timer> = emptyList()

    @JacksonXmlProperty(localName = "math")
    @JacksonXmlElementWrapper(useWrapping = false)
    var mathElements: List<Math> = emptyList()

    @JacksonXmlProperty(localName = "header")
    @JacksonXmlElementWrapper(useWrapping = false)
    var headers: List<Header> = emptyList()

    @JacksonXmlProperty(localName = "groupcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var groupContainers: List<GroupContainer> = emptyList()

    @JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var gridContainers: List<GridContainer> = emptyList()

    @JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var horizontalContainers: List<HorizontalContainer> = emptyList()

    @JacksonXmlProperty(localName = "tabcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var tabContainers: List<TabContainer> = emptyList()

}
