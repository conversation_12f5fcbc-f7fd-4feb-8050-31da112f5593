package com.chipon.configtool.core.model

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Represents a math element for calculations.
 * Maps to <math> elements in the XML.
 */
class Math : BaseElement() {

    @JacksonXmlProperty(isAttribute = true)
    var formula: String? = null

    @JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    var actions: List<Action> = emptyList()

}
