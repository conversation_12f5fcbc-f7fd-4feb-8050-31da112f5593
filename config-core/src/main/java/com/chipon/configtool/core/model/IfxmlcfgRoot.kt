package com.chipon.configtool.core.model

import com.chipon.configtool.core.model.container.VerticalContainer
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement

/**
 * Root element for IFX XML configuration files.
 * Represents the <ifxmlcfg> element with version attribute and child elements.
 */
@JacksonXmlRootElement(localName = "ifxmlcfg")
class IfxmlcfgRoot {

    @JacksonXmlProperty(isAttribute = true)
    var version: String? = null

    @JacksonXmlProperty(localName = "checksum")
    var checksum: String? = null

    @JacksonXmlProperty(localName = "version")
    var versionInfo: Version? = null

    @JacksonXmlProperty(localName = "fwversion")
    var fwVersion: FwVersion? = null

    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var verticalContainers: List<VerticalContainer> = emptyList()

    constructor()

    constructor(
        version: String?,
        checksum: String? = null,
        versionInfo: Version? = null,
        fwVersion: FwVersion? = null,
        verticalContainers: List<VerticalContainer> = emptyList()
    ) {
        this.version = version
        this.checksum = checksum
        this.versionInfo = versionInfo
        this.fwVersion = fwVersion
        this.verticalContainers = verticalContainers
    }
}

/**
 * Version information element.
 */
class Version {

    @JacksonXmlProperty(isAttribute = true)
    var label: String? = null

    constructor()

    constructor(label: String?) {
        this.label = label
    }
}

/**
 * Firmware version element with min and max version constraints.
 */
class FwVersion {

    @JacksonXmlProperty(isAttribute = true)
    var min: String? = null

    @JacksonXmlProperty(isAttribute = true)
    var max: String? = null

    constructor()

    constructor(min: String?, max: String?) {
        this.min = min
        this.max = max
    }
}
