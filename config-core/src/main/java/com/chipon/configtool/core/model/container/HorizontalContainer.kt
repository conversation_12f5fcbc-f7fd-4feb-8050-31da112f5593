package com.chipon.configtool.core.model.container

import com.chipon.configtool.core.model.BaseContainer
import com.chipon.configtool.core.model.control.*
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Represents a horizontal container element.
 * Maps to <horizontalcontainer> elements in the XML.
 */
class HorizontalContainer : BaseContainer() {

    @JacksonXmlProperty(isAttribute = true)
    override var visible: Boolean? = null

    @JacksonXmlProperty(isAttribute = true)
    override var color: String? = null

    @JacksonXmlProperty(isAttribute = true)
    override var sizepolicy: String? = null

    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null

    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var verticalContainers: List<VerticalContainer> = emptyList()

    @JacksonXmlProperty(localName = "groupcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var groupContainers: List<GroupContainer> = emptyList()

    @JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var gridContainers: List<GridContainer> = emptyList()

    @JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var horizontalContainers: List<HorizontalContainer> = emptyList()

    @JacksonXmlProperty(localName = "button")
    @JacksonXmlElementWrapper(useWrapping = false)
    var buttons: List<Button> = emptyList()

    @JacksonXmlProperty(localName = "text")
    @JacksonXmlElementWrapper(useWrapping = false)
    var textElements: List<Text> = emptyList()

    @JacksonXmlProperty(localName = "led")
    @JacksonXmlElementWrapper(useWrapping = false)
    var leds: List<Led> = emptyList()

    @JacksonXmlProperty(localName = "math")
    @JacksonXmlElementWrapper(useWrapping = false)
    var mathElements: List<com.chipon.configtool.core.model.Math> = emptyList()

    @JacksonXmlProperty(localName = "checkbox")
    @JacksonXmlElementWrapper(useWrapping = false)
    var checkboxes: List<Checkbox> = emptyList()

    @JacksonXmlProperty(localName = "combo")
    @JacksonXmlElementWrapper(useWrapping = false)
    var combos: List<Combo> = emptyList()

    @JacksonXmlProperty(localName = "radio")
    @JacksonXmlElementWrapper(useWrapping = false)
    var radios: List<Radio> = emptyList()

    @JacksonXmlProperty(localName = "doublespinbox")
    @JacksonXmlElementWrapper(useWrapping = false)
    var doubleSpinBoxes: List<DoubleSpinBox> = emptyList()

    @JacksonXmlProperty(localName = "togglebutton")
    @JacksonXmlElementWrapper(useWrapping = false)
    var toggleButtons: List<ToggleButton> = emptyList()
}
