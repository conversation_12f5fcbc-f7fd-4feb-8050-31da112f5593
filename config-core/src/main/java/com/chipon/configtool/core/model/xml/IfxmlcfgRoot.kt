package com.chipon.configtool.core.model.xml

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement

/**
 * Root element for the ifxmlcfg XML schema.
 * Represents the top-level configuration container.
 * 
 * <AUTHOR> Model
 */
@JacksonXmlRootElement(localName = "ifxmlcfg")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class IfxmlcfgRoot(
    
    @JacksonXmlProperty(isAttribute = true)
    val version: String? = null,
    
    @JacksonXmlProperty(localName = "checksum")
    val checksum: String? = null,
    
    @JacksonXmlProperty(localName = "version")
    val versionInfo: Version? = null,
    
    @JacksonXmlProperty(localName = "fwversion")
    val fwVersion: FwVersion? = null,
    
    @JacksonXmlProperty(localName = "svd")
    val svd: SvdReference? = null,
    
    @JacksonXmlProperty(localName = "loadsaveallowed")
    val loadSaveAllowed: LoadSaveAllowed? = null,
    
    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null,
    
    @JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer>? = null,
    
    @JacksonXmlProperty(localName = "header")
    @JacksonXmlElementWrapper(useWrapping = false)
    val headers: List<Header>? = null
    
) : BaseElement()

/**
 * Version information element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Version(
    @JacksonXmlProperty(isAttribute = true)
    val label: String? = null
) : BaseElement()

/**
 * Firmware version specification.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class FwVersion(
    @JacksonXmlProperty(isAttribute = true)
    val min: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    val max: String? = null
) : BaseElement()

/**
 * SVD file reference.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class SvdReference(
    @JacksonXmlProperty(isAttribute = true)
    val file: String? = null
) : BaseElement()

/**
 * Load/save allowed configuration.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class LoadSaveAllowed(
    @JacksonXmlProperty(isAttribute = true)
    val value: String? = null
) : BaseElement()
