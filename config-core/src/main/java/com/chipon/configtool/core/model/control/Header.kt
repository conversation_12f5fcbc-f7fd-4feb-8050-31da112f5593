package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseElement
import com.chipon.configtool.core.model.container.*

/**
 * Represents a header element.
 * Maps to <header> elements in the XML.
 */
data class Header(
    @field:JacksonXmlProperty(isAttribute = true)
    override val visible: Boolean? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val file: String? = null,
    
    @field:JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val gridContainers: List<GridContainer> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "groupcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val groupContainers: List<GroupContainer> = emptyList()
) : BaseElement(visible, define)
