package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseElement
import com.chipon.configtool.core.model.GridContainer
import com.chipon.configtool.core.model.GroupContainer
import com.chipon.configtool.core.model.HorizontalContainer
import com.chipon.configtool.core.model.VerticalContainer

/**
 * Represents a header element.
 * Maps to <header> elements in the XML.
 */
class Header : BaseElement {

    @JacksonXmlProperty(isAttribute = true)
    var file: String? = null

    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var verticalContainers: List<VerticalContainer> = emptyList()

    @JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var horizontalContainers: List<HorizontalContainer> = emptyList()

    @JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var gridContainers: List<GridContainer> = emptyList()

    @JacksonXmlProperty(localName = "groupcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    var groupContainers: List<GroupContainer> = emptyList()

    constructor() : super()

    constructor(
        define: String? = null,
        visible: Boolean? = null,
        file: String? = null,
        verticalContainers: List<VerticalContainer> = emptyList(),
        horizontalContainers: List<HorizontalContainer> = emptyList(),
        gridContainers: List<GridContainer> = emptyList(),
        groupContainers: List<GroupContainer> = emptyList()
    ) : super(define, visible) {
        this.file = file
        this.verticalContainers = verticalContainers
        this.horizontalContainers = horizontalContainers
        this.gridContainers = gridContainers
        this.groupContainers = groupContainers
    }
}
