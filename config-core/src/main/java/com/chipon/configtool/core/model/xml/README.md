# Jackson XML Model Definitions for ifxmlcfg Schema

This package contains Kotlin data classes that represent the XML structure used in the ifxmlcfg configuration files. The models use Jackson XML annotations to enable seamless serialization and deserialization of XML files.

## Overview

The XML model hierarchy is designed with proper object-oriented inheritance to handle the complex structure of the ifxmlcfg schema:

### Base Classes

- **`BaseElement`** - Abstract base class for all XML elements with common attributes like `visible`, `define`, and `svd`
- **`ContainerElement`** - Base class for container elements that can hold other elements (extends `BaseElement`)
- **`UIElement`** - Base class for UI elements that represent interactive components (extends `BaseElement`)

### Root Element

- **`IfxmlcfgRoot`** - The root `<ifxmlcfg>` element that contains all other elements

### Core Elements

- **`Version`** - Version information with label attribute
- **`FwVersion`** - Firmware version specification with min/max attributes
- **`SvdReference`** - SVD file reference
- **`LoadSaveAllowed`** - Load/save configuration

### Container Elements

- **`VerticalContainer`** - Arranges child elements vertically
- **`HorizontalContainer`** - Arranges child elements horizontally  
- **`GridContainer`** - Arranges child elements in a grid layout
- **`TabContainer`** - Provides tabbed interface
- **`GroupContainer`** - Groups related elements
- **`Header`** - Header information container

### UI Elements

- **`Variable`** - Variable definition
- **`Timer`** - Timer for periodic actions
- **`Math`** - Mathematical calculations
- **`Button`** - Button element
- **`Checkbox`** - Checkbox element
- **`Combo`** - Combo box with items
- **`Text`** - Text element
- **`Led`** - LED indicator
- **`Radio`** - Radio button group
- **`ToggleButton`** - Toggle button
- **`DoubleSpinBox`** - Double spin box
- **`HorizontalLine`** - Horizontal line separator
- **`Action`** - Event handling action
- **`Item`** - Item for combo boxes and lists

## Usage

### Basic Deserialization

```kotlin
import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import java.io.File

val xmlMapper = XmlMapper().apply {
    registerKotlinModule()
}

val xmlFile = File("path/to/your/file.xml")
val ifxmlcfg = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)

// Access parsed data
println("Version: ${ifxmlcfg.version}")
println("Version Info: ${ifxmlcfg.versionInfo?.label}")
println("FW Version: ${ifxmlcfg.fwVersion?.min} - ${ifxmlcfg.fwVersion?.max}")
```

### Serialization

```kotlin
val serializedXml = xmlMapper.writeValueAsString(ifxmlcfg)
println(serializedXml)
```

### Accessing Container Elements

```kotlin
ifxmlcfg.verticalContainers?.forEach { container ->
    println("Container: ${container.label}")
    
    // Access variables
    container.variables?.forEach { variable ->
        println("Variable: ${variable.define}")
    }
    
    // Access timers
    container.timers?.forEach { timer ->
        println("Timer: ${timer.define}, interval: ${timer.interval}")
    }
    
    // Access UI elements
    container.headers?.forEach { header ->
        println("Header file: ${header.file}")
    }
}
```

### Working with UI Elements

```kotlin
// Find specific elements
val initTimer = container.timers?.find { it.define == "INIT" }
initTimer?.actions?.forEach { action ->
    println("Action: ${action.event} -> ${action.cmd}")
}

// Access combo box items
container.horizontalContainers?.forEach { hContainer ->
    hContainer.combos?.forEach { combo ->
        combo.items?.forEach { item ->
            println("Item: ${item.label} = ${item.value}")
        }
    }
}
```

## Testing

### Running Tests

Use the provided test utilities to validate the models:

```kotlin
// Run the test runner
fun main() {
    com.chipon.configtool.core.model.xml.main()
}
```

### Validation Utility

```kotlin
import com.chipon.configtool.core.model.xml.XmlModelValidator

// Validate all files
val result = XmlModelValidator.validateAllCaseFiles()
println(result.message)

// Validate specific file
val fileResult = XmlModelValidator.validateFile("ICW_TLE9263.xml")
println(fileResult.message)

// Test round-trip serialization
val roundTripResult = XmlModelValidator.testRoundTrip("ICW_TLE9263.xml")
println(roundTripResult.message)
```

## Key Features

### Polymorphic Support

The model supports polymorphic deserialization where containers can hold various types of child elements. This is achieved through the use of `@JacksonXmlElementWrapper(useWrapping = false)` and multiple property lists for different element types.

### Flexible Attribute Handling

All elements inherit common attributes from their base classes, while specific elements can define their own attributes. The `@JsonIgnoreProperties(ignoreUnknown = true)` annotation ensures that unknown attributes don't cause parsing failures.

### Null Safety

All properties are nullable to handle optional XML attributes and elements gracefully.

### Round-trip Compatibility

The models are designed to support round-trip serialization/deserialization, meaning you can parse an XML file, modify the object, and serialize it back to XML while preserving the structure.

## File Structure

```
config-core/src/main/java/com/chipon/configtool/core/model/xml/
├── BaseElement.kt           # Base classes and common functionality
├── IfxmlcfgRoot.kt         # Root element and core elements
├── ContainerElements.kt     # Container element definitions
├── UIElements.kt           # UI element definitions
├── XmlModelValidator.kt    # Validation utilities
├── TestRunner.kt           # Simple test runner
└── README.md              # This documentation
```

## Dependencies

The models require the following dependencies (already included in the project):

- `com.fasterxml.jackson.dataformat:jackson-dataformat-xml`
- `com.fasterxml.jackson.module:jackson-module-kotlin`

## Notes

- The models are designed to handle the specific XML schema used in the ifxmlcfg files
- All XML files in the `config-core/src/main/resources/case/` directory should be parseable with these models
- The inheritance hierarchy allows for easy extension and modification of the model structure
- Error handling is built-in through Jackson's robust XML parsing capabilities
