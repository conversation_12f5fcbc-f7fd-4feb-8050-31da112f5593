package com.chipon.configtool.core.model

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.action.Action

/**
 * Represents a math element for calculations.
 * Maps to <math> elements in the XML.
 */
class Math : BaseElement {

    @JacksonXmlProperty(isAttribute = true)
    var formula: String? = null

    @JacksonXmlProperty(isAttribute = true)
    var format: String? = null

    @JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    var actions: List<Action> = emptyList()

    constructor() : super()

    constructor(
        define: String?,
        visible: Boolean? = null,
        formula: String? = null,
        format: String? = null,
        actions: List<Action> = emptyList()
    ) : super(define, visible) {
        this.formula = formula
        this.format = format
        this.actions = actions
    }
}
