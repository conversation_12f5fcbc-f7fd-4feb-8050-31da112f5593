package com.chipon.configtool.core.model

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.action.Action

/**
 * Represents a math element for calculations.
 * Maps to <math> elements in the XML.
 */
data class Math(
    @field:JacksonXmlProperty(isAttribute = true)
    override val define: String,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val visible: Boolean? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val formula: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val format: String? = null,
    
    @field:JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    val actions: List<Action> = emptyList()
) : BaseElement(visible, define)
