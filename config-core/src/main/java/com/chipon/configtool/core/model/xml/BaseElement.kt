package com.chipon.configtool.core.model.xml

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude

/**
 * Base class for all XML elements in the ifxmlcfg schema.
 * Provides common functionality and properties shared across all elements.
 * 
 * <AUTHOR> Model
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
abstract class BaseElement {
    
    /**
     * Common attributes that might appear on any element
     */
    var visible: String? = null
    var define: String? = null
    var svd: String? = null
    
    override fun toString(): String {
        return "${this::class.simpleName}(define=$define, visible=$visible)"
    }
}

/**
 * Base class for container elements that can hold other elements.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
abstract class ContainerElement : BaseElement() {
    
    /**
     * Common container attributes
     */
    var label: String? = null
    var color: String? = null
    var sizepolicy: String? = null
    var columns: String? = null
    
    override fun toString(): String {
        return "${this::class.simpleName}(label=$label, define=$define)"
    }
}

/**
 * Base class for UI elements that represent interactive components.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
abstract class UIElement : BaseElement() {
    
    /**
     * Common UI element attributes
     */
    var size: String? = null
    var data: String? = null
    var bitmask: String? = null
    var oncolor: String? = null
    var offcolor: String? = null
    var range: String? = null
    var default: String? = null
    var step: String? = null
    var suffix: String? = null
    var role: String? = null
    var lockon: String? = null
    
    override fun toString(): String {
        return "${this::class.simpleName}(define=$define, data=$data)"
    }
}
