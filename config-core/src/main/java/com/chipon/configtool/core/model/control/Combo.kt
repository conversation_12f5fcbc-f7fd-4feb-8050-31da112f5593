package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseUIControl

/**
 * Represents a combo box UI control element.
 * Maps to <combo> elements in the XML.
 */
data class Combo(
    @JacksonXmlProperty(isAttribute = true)
    override var visible: Boolean? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var size: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var data: String? = null,
    
    @JacksonXmlProperty(localName = "item")
    @JacksonXmlElementWrapper(useWrapping = false)
    var items: List<ComboItem> = emptyList()
) : BaseUIControl(visible, define, label, size, data)

/**
 * Represents an item in a combo box.
 */
data class ComboItem(
    @JacksonXmlProperty(isAttribute = true)
    var label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    var varue: String? = null
)
