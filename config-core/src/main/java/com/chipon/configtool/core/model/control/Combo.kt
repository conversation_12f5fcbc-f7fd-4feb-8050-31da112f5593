package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseUIControl

/**
 * Represents a combo box UI control element.
 * Maps to <combo> elements in the XML.
 */
data class Combo(
    @field:JacksonXmlProperty(isAttribute = true)
    override val visible: Boolean? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val size: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val data: String? = null,
    
    @field:JacksonXmlProperty(localName = "item")
    @JacksonXmlElementWrapper(useWrapping = false)
    val items: List<ComboItem> = emptyList()
) : BaseUIControl(visible, define, label, size, data)

/**
 * Represents an item in a combo box.
 */
data class ComboItem(
    @field:JacksonXmlProperty(isAttribute = true)
    val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val value: String? = null
)
