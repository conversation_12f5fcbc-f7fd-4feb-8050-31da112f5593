package com.chipon.configtool.core.model

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Base class for all XML elements with common attributes.
 */
abstract class BaseElement {

    @JacksonXmlProperty(isAttribute = true)
    open var id: String? = null

}

/**
 * Base class for UI control elements.
 */
abstract class BaseUIControl : BaseElement() {

    @JacksonXmlProperty(isAttribute = true)
    open var label: String? = null

    @JacksonXmlProperty(isAttribute = true)
    open var size: String? = null

    @JacksonXmlProperty(isAttribute = true)
    open var data: String? = null

}
