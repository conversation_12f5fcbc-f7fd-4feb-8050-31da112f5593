package com.chipon.configtool.core.model

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Base class for all XML elements with common attributes.
 */
abstract class BaseElement(
    @field:JacksonXmlProperty(isAttribute = true)
    open val visible: Boolean? = null,

    @field:JacksonXmlProperty(isAttribute = true)
    open val define: String? = null
)

/**
 * Base class for container elements with common container attributes.
 */
abstract class BaseContainer(
    visible: Boolean? = null,
    define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    open val color: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    open val sizepolicy: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    open val label: String? = null
) : BaseElement(visible, define)

/**
 * Base class for UI control elements.
 */
abstract class BaseUIControl(
    visible: Boolean? = null,
    define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    open val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    open val size: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    open val data: String? = null
) : BaseElement(visible, define)
