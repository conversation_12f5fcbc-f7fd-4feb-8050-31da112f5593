package com.chipon.configtool.core.model.xml

import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import java.io.File

/**
 * Example usage of the Jackson XML model definitions.
 * Demonstrates how to parse, manipulate, and serialize ifxmlcfg files.
 * 
 * <AUTHOR> Example
 */
object XmlModelExample {
    
    private val xmlMapper = XmlMapper().apply {
        registerKotlinModule()
    }
    
    /**
     * Example: Parse an XML file and extract information.
     */
    fun parseAndExtractInfo(fileName: String) {
        println("=== Parsing $fileName ===")
        
        val xmlFile = File("config-core/src/main/resources/case/$fileName")
        if (!xmlFile.exists()) {
            println("File not found: ${xmlFile.absolutePath}")
            return
        }
        
        try {
            val ifxmlcfg = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
            
            // Extract basic information
            println("Root Version: ${ifxmlcfg.version}")
            println("Checksum: ${ifxmlcfg.checksum}")
            println("Version Label: ${ifxmlcfg.versionInfo?.label}")
            println("Firmware Version: ${ifxmlcfg.fwVersion?.min} to ${ifxmlcfg.fwVersion?.max}")
            
            // Check for optional elements
            ifxmlcfg.svd?.let { svd ->
                println("SVD File: ${svd.file}")
            }
            
            ifxmlcfg.loadSaveAllowed?.let { loadSave ->
                println("Load/Save Allowed: ${loadSave.value}")
            }
            
            // Analyze container structure
            analyzeContainers(ifxmlcfg)
            
        } catch (e: Exception) {
            println("Error parsing file: ${e.message}")
            e.printStackTrace()
        }
    }
    
    /**
     * Analyze the container structure and UI elements.
     */
    private fun analyzeContainers(ifxmlcfg: IfxmlcfgRoot) {
        println("\n--- Container Analysis ---")
        
        ifxmlcfg.verticalContainers?.forEachIndexed { index, container ->
            println("Vertical Container $index:")
            println("  Color: ${container.color}")
            println("  Size Policy: ${container.sizepolicy}")
            
            // Count different types of elements
            val elementCounts = mutableMapOf<String, Int>()
            
            container.variables?.let { elementCounts["Variables"] = it.size }
            container.timers?.let { elementCounts["Timers"] = it.size }
            container.mathElements?.let { elementCounts["Math Elements"] = it.size }
            container.headers?.let { elementCounts["Headers"] = it.size }
            container.verticalContainers?.let { elementCounts["Nested Vertical Containers"] = it.size }
            container.horizontalContainers?.let { elementCounts["Horizontal Containers"] = it.size }
            container.gridContainers?.let { elementCounts["Grid Containers"] = it.size }
            container.tabContainers?.let { elementCounts["Tab Containers"] = it.size }
            container.groupContainers?.let { elementCounts["Group Containers"] = it.size }
            
            elementCounts.forEach { (type, count) ->
                println("  $type: $count")
            }
            
            // Analyze specific elements
            analyzeVariables(container.variables)
            analyzeTimers(container.timers)
            analyzeHeaders(container.headers)
        }
    }
    
    /**
     * Analyze variable definitions.
     */
    private fun analyzeVariables(variables: List<Variable>?) {
        variables?.let { vars ->
            if (vars.isNotEmpty()) {
                println("  Variable Details:")
                vars.take(5).forEach { variable ->
                    println("    - ${variable.define} (svd: ${variable.svd})")
                }
                if (vars.size > 5) {
                    println("    ... and ${vars.size - 5} more variables")
                }
            }
        }
    }
    
    /**
     * Analyze timer configurations.
     */
    private fun analyzeTimers(timers: List<Timer>?) {
        timers?.let { timerList ->
            if (timerList.isNotEmpty()) {
                println("  Timer Details:")
                timerList.forEach { timer ->
                    println("    - ${timer.define}: interval=${timer.interval}, singleShot=${timer.singleShot}, run=${timer.run}")
                    timer.actions?.let { actions ->
                        println("      Actions: ${actions.size}")
                        actions.take(2).forEach { action ->
                            println("        ${action.event} -> ${action.cmd}")
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Analyze header information.
     */
    private fun analyzeHeaders(headers: List<Header>?) {
        headers?.let { headerList ->
            if (headerList.isNotEmpty()) {
                println("  Header Details:")
                headerList.forEach { header ->
                    println("    - File: ${header.file}")
                    header.gridContainers?.let { grids ->
                        println("      Grid Containers: ${grids.size}")
                    }
                    header.verticalContainers?.let { vContainers ->
                        println("      Vertical Containers: ${vContainers.size}")
                    }
                }
            }
        }
    }
    
    /**
     * Example: Create a new ifxmlcfg structure programmatically.
     */
    fun createNewConfiguration(): IfxmlcfgRoot {
        println("\n=== Creating New Configuration ===")
        
        val newConfig = IfxmlcfgRoot(
            version = "2.0.4",
            checksum = "example-checksum-12345",
            versionInfo = Version(label = "V1.0.0"),
            fwVersion = FwVersion(min = "1.0.0", max = "2.0.0"),
            verticalContainers = listOf(
                VerticalContainer(
                    color = "255;255;255;255",
                    sizepolicy = "fixed;fixed",
                    variables = listOf(
                        Variable(define = "EXAMPLE.VAR1"),
                        Variable(define = "EXAMPLE.VAR2", svd = "1")
                    ),
                    timers = listOf(
                        Timer(
                            define = "EXAMPLE_TIMER",
                            interval = "1000",
                            singleShot = "0",
                            run = "1",
                            actions = listOf(
                                Action(
                                    event = "changed",
                                    cmd = "sendUSB",
                                    data = "0x01;0x02;0x03"
                                )
                            )
                        )
                    )
                )
            )
        )
        
        return newConfig
    }
    
    /**
     * Example: Serialize configuration to XML.
     */
    fun serializeToXml(config: IfxmlcfgRoot): String {
        println("\n=== Serializing to XML ===")
        
        return try {
            val xmlString = xmlMapper.writeValueAsString(config)
            println("Serialization successful!")
            println("XML Length: ${xmlString.length} characters")
            xmlString
        } catch (e: Exception) {
            println("Serialization failed: ${e.message}")
            ""
        }
    }
    
    /**
     * Example: Round-trip test (parse -> modify -> serialize -> parse).
     */
    fun roundTripTest(fileName: String) {
        println("\n=== Round-trip Test for $fileName ===")
        
        val xmlFile = File("config-core/src/main/resources/case/$fileName")
        if (!xmlFile.exists()) {
            println("File not found: ${xmlFile.absolutePath}")
            return
        }
        
        try {
            // Step 1: Parse original
            val original = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
            println("✓ Original parsed successfully")
            
            // Step 2: Modify (example: change checksum)
            val modified = original.copy(checksum = "modified-${original.checksum}")
            println("✓ Configuration modified")
            
            // Step 3: Serialize
            val xmlString = xmlMapper.writeValueAsString(modified)
            println("✓ Serialized to XML (${xmlString.length} chars)")
            
            // Step 4: Parse serialized version
            val roundTrip = xmlMapper.readValue(xmlString, IfxmlcfgRoot::class.java)
            println("✓ Round-trip parsing successful")
            
            // Step 5: Verify
            if (roundTrip.version == original.version && 
                roundTrip.versionInfo?.label == original.versionInfo?.label) {
                println("✓ Round-trip verification successful")
            } else {
                println("⚠ Round-trip verification found differences")
            }
            
        } catch (e: Exception) {
            println("❌ Round-trip test failed: ${e.message}")
        }
    }
}

/**
 * Main function to run examples.
 */
fun main() {
    println("Jackson XML Model Examples")
    println("==========================")
    
    // Example 1: Parse existing files
    val testFiles = listOf("ICW_TLE9263.xml", "ICW_TLE9273.xml", "ICW_TLE94x1.xml")
    
    testFiles.forEach { fileName ->
        XmlModelExample.parseAndExtractInfo(fileName)
        println()
    }
    
    // Example 2: Create new configuration
    val newConfig = XmlModelExample.createNewConfiguration()
    val xmlString = XmlModelExample.serializeToXml(newConfig)
    
    if (xmlString.isNotEmpty()) {
        println("Generated XML preview:")
        println(xmlString.take(500) + "...")
        println()
    }
    
    // Example 3: Round-trip test
    XmlModelExample.roundTripTest("ICW_TLE9263.xml")
}
