package com.chipon.configtool.core.model.container

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseContainer
import com.chipon.configtool.core.model.control.*

/**
 * Represents a grid container element.
 * Maps to <gridcontainer> elements in the XML.
 */
data class GridContainer(
    @field:JacksonXmlProperty(isAttribute = true)
    override val visible: Boolean? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val color: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val sizepolicy: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val columns: String? = null,
    
    @field:JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "groupcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val groupContainers: List<GroupContainer> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val gridContainers: List<GridContainer> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "button")
    @JacksonXmlElementWrapper(useWrapping = false)
    val buttons: List<Button> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "text")
    @JacksonXmlElementWrapper(useWrapping = false)
    val textElements: List<Text> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "led")
    @JacksonXmlElementWrapper(useWrapping = false)
    val leds: List<Led> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "checkbox")
    @JacksonXmlElementWrapper(useWrapping = false)
    val checkboxes: List<Checkbox> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "combo")
    @JacksonXmlElementWrapper(useWrapping = false)
    val combos: List<Combo> = emptyList(),
    
    @field:JacksonXmlProperty(localName = "radio")
    @JacksonXmlElementWrapper(useWrapping = false)
    val radios: List<Radio> = emptyList()
) : BaseContainer(visible, define, color, sizepolicy, label)
