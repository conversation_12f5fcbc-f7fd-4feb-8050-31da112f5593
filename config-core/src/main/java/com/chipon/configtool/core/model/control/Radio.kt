package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseUIControl

/**
 * Represents a radio button group UI control element.
 * Maps to <radio> elements in the XML.
 */
data class Radio(
    @field:JacksonXmlProperty(isAttribute = true)
    override val visible: Boolean? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val size: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val data: String? = null,
    
    @field:JacksonXmlProperty(localName = "radiobutton")
    @JacksonXmlElementWrapper(useWrapping = false)
    val radioButtons: List<RadioButton> = emptyList()
) : BaseUIControl(visible, define, label, size, data)

/**
 * Represents a radio button within a radio group.
 */
data class RadioButton(
    @field:JacksonXmlProperty(isAttribute = true)
    val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val value: String? = null
)
