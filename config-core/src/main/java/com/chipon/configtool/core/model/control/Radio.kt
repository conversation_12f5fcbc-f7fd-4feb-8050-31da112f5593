package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseUIControl

/**
 * Represents a radio button group UI control element.
 * Maps to <radio> elements in the XML.
 */
data class Radio(
    @JacksonXmlProperty(isAttribute = true)
    override var visible: Boolean? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var size: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var data: String? = null,
    
    @JacksonXmlProperty(localName = "radiobutton")
    @JacksonXmlElementWrapper(useWrapping = false)
    var radioButtons: List<RadioButton> = emptyList()
) : BaseUIControl(visible, define, label, size, data)

/**
 * Represents a radio button within a radio group.
 */
data class RadioButton(
    @JacksonXmlProperty(isAttribute = true)
    var label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    var varue: String? = null
)
