package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseUIControl
import com.chipon.configtool.core.model.extra.Action

/**
 * Represents a button UI control element.
 * Maps to <button> elements in the XML.
 */
class Button : BaseUIControl {

    @JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    var actions: List<Action> = emptyList()

    constructor() : super()

    constructor(
        define: String? = null,
        visible: Boolean? = null,
        label: String? = null,
        size: String? = null,
        data: String? = null,
        actions: List<Action> = emptyList()
    ) : super(define, visible, label, size, data) {
        this.actions = actions
    }
}
