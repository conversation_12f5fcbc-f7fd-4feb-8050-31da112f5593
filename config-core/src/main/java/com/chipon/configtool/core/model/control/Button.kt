package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseUIControl
import com.chipon.configtool.core.model.action.Action

/**
 * Represents a button UI control element.
 * Maps to <button> elements in the XML.
 */
data class Button(
    @field:JacksonXmlProperty(isAttribute = true)
    override val visible: Boolean? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val size: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val data: String? = null,
    
    @field:JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    val actions: List<Action> = emptyList()
) : BaseUIControl(visible, define, label, size, data)
