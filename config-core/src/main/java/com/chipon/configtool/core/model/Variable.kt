package com.chipon.configtool.core.model

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Represents a variable definition element.
 * Maps to <var> elements in the XML.
 */
data class Variable(
    @field:JacksonXmlProperty(isAttribute = true)
    override val define: String,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val visible: Boolean? = null
) : BaseElement(visible, define)
