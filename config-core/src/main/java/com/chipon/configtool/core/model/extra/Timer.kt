package com.chipon.configtool.core.model

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Represents a timer element.
 * Maps to <timer> elements in the XML.
 */
class Timer : BaseElement() {

    @JacksonXmlProperty(isAttribute = true)
    var intervar: String? = null

    @JacksonXmlProperty(isAttribute = true)
    var singleShot: String? = null

    @JacksonXmlProperty(isAttribute = true)
    var run: String? = null

    @JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    var actions: List<Action> = emptyList()

}
