package com.chipon.configtool.core.model.xml

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText

/**
 * Variable definition element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Variable(
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var svd: String? = null
) : UIElement()

/**
 * Timer element for periodic actions.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Timer(
    @JacksonXmlProperty(isAttribute = true)
    override var visible: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    val interval: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    val singleShot: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    val run: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var svd: String? = null,
    
    @JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    val actions: List<Action>? = null
) : UIElement()

/**
 * Math element for calculations.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Math(
    @JacksonXmlProperty(isAttribute = true)
    override var visible: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    val formula: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    val format: String? = null,
    
    @JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    val actions: List<Action>? = null
) : UIElement()

/**
 * Button element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Button(
    @JacksonXmlProperty(isAttribute = true)
    val label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var size: String? = null,
    
    @JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    val actions: List<Action>? = null
) : UIElement()

/**
 * Checkbox element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Checkbox(
    @JacksonXmlProperty(isAttribute = true)
    val label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var default: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var role: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var lockon: String? = null
) : UIElement()

/**
 * Combo box element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Combo(
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(localName = "item")
    @JacksonXmlElementWrapper(useWrapping = false)
    val items: List<Item>? = null
) : UIElement()

/**
 * Item element for combo boxes and other lists.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Item(
    @JacksonXmlProperty(isAttribute = true)
    val label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    val value: String? = null
) : UIElement()

/**
 * Text element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Text(
    @JacksonXmlProperty(isAttribute = true)
    val label: String? = null,
    
    @JacksonXmlText
    val content: String? = null
) : UIElement()

/**
 * LED indicator element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Led(
    @JacksonXmlProperty(isAttribute = true)
    override var data: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var bitmask: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var oncolor: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var offcolor: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var size: String? = null
) : UIElement()

/**
 * Double spin box element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class DoubleSpinBox(
    @JacksonXmlProperty(isAttribute = true)
    override var suffix: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var range: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var default: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var step: String? = null
) : UIElement()

/**
 * Radio button group element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Radio(
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(localName = "radiobutton")
    @JacksonXmlElementWrapper(useWrapping = false)
    val radioButtons: List<RadioButton>? = null
) : UIElement()

/**
 * Individual radio button element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class RadioButton(
    @JacksonXmlProperty(isAttribute = true)
    val label: String? = null
) : UIElement()

/**
 * Toggle button element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class ToggleButton(
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    val label: String? = null,
    
    @JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    val actions: List<Action>? = null
) : UIElement()

/**
 * Horizontal line separator element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class HorizontalLine(
    @JacksonXmlProperty(isAttribute = true)
    override var size: String? = null
) : UIElement()

/**
 * Action element for event handling.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Action(
    @JacksonXmlProperty(isAttribute = true)
    val event: String? = null,

    @JacksonXmlProperty(isAttribute = true)
    val cmd: String? = null,

    @JacksonXmlProperty(isAttribute = true)
    override var data: String? = null,

    @JacksonXmlProperty(isAttribute = true)
    val recdata: String? = null
) : UIElement()

/**
 * Separator element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Separator(
    @JacksonXmlProperty(isAttribute = true)
    override var size: String? = null
) : UIElement()

/**
 * Field element for forms.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Field(
    @JacksonXmlProperty(isAttribute = true)
    val label: String? = null,

    // Child elements
    @JacksonXmlProperty(localName = "text")
    @JacksonXmlElementWrapper(useWrapping = false)
    val textElements: List<Text>? = null,

    @JacksonXmlProperty(localName = "combo")
    @JacksonXmlElementWrapper(useWrapping = false)
    val combos: List<Combo>? = null
) : UIElement()

/**
 * Fieldset element for grouping form fields.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Fieldset(
    @JacksonXmlProperty(isAttribute = true)
    val label: String? = null,

    @JacksonXmlProperty(localName = "field")
    @JacksonXmlElementWrapper(useWrapping = false)
    val fields: List<Field>? = null
) : UIElement()

/**
 * Form element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Form(
    @JacksonXmlProperty(localName = "fieldset")
    @JacksonXmlElementWrapper(useWrapping = false)
    val fieldsets: List<Fieldset>? = null
) : UIElement()
