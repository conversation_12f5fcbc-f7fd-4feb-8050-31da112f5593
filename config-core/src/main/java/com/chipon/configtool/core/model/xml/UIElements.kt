package com.chipon.configtool.core.model.xml

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText

/**
 * Variable definition element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Variable(
    @field:JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var svd: String? = null
) : UIElement()

/**
 * Timer element for periodic actions.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Timer(
    @field:JacksonXmlProperty(isAttribute = true)
    override var visible: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val interval: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val singleShot: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val run: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var svd: String? = null,
    
    @field:JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    val actions: List<Action>? = null
) : UIElement()

/**
 * Math element for calculations.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Math(
    @field:JacksonXmlProperty(isAttribute = true)
    override var visible: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val formula: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val format: String? = null,
    
    @field:JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    val actions: List<Action>? = null
) : UIElement()

/**
 * Button element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Button(
    @field:JacksonXmlProperty(isAttribute = true)
    val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var size: String? = null,
    
    @field:JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    val actions: List<Action>? = null
) : UIElement()

/**
 * Checkbox element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Checkbox(
    @field:JacksonXmlProperty(isAttribute = true)
    val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var default: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var role: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var lockon: String? = null
) : UIElement()

/**
 * Combo box element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Combo(
    @field:JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @field:JacksonXmlProperty(localName = "item")
    @JacksonXmlElementWrapper(useWrapping = false)
    val items: List<Item>? = null
) : UIElement()

/**
 * Item element for combo boxes and other lists.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Item(
    @field:JacksonXmlProperty(isAttribute = true)
    val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val value: String? = null
) : UIElement()

/**
 * Text element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Text(
    @field:JacksonXmlProperty(isAttribute = true)
    val label: String? = null,
    
    @JacksonXmlText
    val content: String? = null
) : UIElement()

/**
 * LED indicator element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Led(
    @field:JacksonXmlProperty(isAttribute = true)
    override var data: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var bitmask: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var oncolor: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var offcolor: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var size: String? = null
) : UIElement()

/**
 * Double spin box element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class DoubleSpinBox(
    @field:JacksonXmlProperty(isAttribute = true)
    override var suffix: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var range: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var default: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var step: String? = null
) : UIElement()

/**
 * Radio button group element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Radio(
    @field:JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @field:JacksonXmlProperty(localName = "radiobutton")
    @JacksonXmlElementWrapper(useWrapping = false)
    val radioButtons: List<RadioButton>? = null
) : UIElement()

/**
 * Individual radio button element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class RadioButton(
    @field:JacksonXmlProperty(isAttribute = true)
    val label: String? = null
) : UIElement()

/**
 * Toggle button element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class ToggleButton(
    @field:JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val label: String? = null,
    
    @field:JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    val actions: List<Action>? = null
) : UIElement()

/**
 * Horizontal line separator element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class HorizontalLine(
    @field:JacksonXmlProperty(isAttribute = true)
    override var size: String? = null
) : UIElement()

/**
 * Action element for event handling.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Action(
    @field:JacksonXmlProperty(isAttribute = true)
    val event: String? = null,

    @field:JacksonXmlProperty(isAttribute = true)
    val cmd: String? = null,

    @field:JacksonXmlProperty(isAttribute = true)
    override var data: String? = null,

    @field:JacksonXmlProperty(isAttribute = true)
    val recdata: String? = null
) : UIElement()

/**
 * Separator element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Separator(
    @field:JacksonXmlProperty(isAttribute = true)
    override var size: String? = null
) : UIElement()

/**
 * Field element for forms.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Field(
    @field:JacksonXmlProperty(isAttribute = true)
    val label: String? = null,

    // Child elements
    @field:JacksonXmlProperty(localName = "text")
    @JacksonXmlElementWrapper(useWrapping = false)
    val textElements: List<Text>? = null,

    @field:JacksonXmlProperty(localName = "combo")
    @JacksonXmlElementWrapper(useWrapping = false)
    val combos: List<Combo>? = null
) : UIElement()

/**
 * Fieldset element for grouping form fields.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Fieldset(
    @field:JacksonXmlProperty(isAttribute = true)
    val label: String? = null,

    @field:JacksonXmlProperty(localName = "field")
    @JacksonXmlElementWrapper(useWrapping = false)
    val fields: List<Field>? = null
) : UIElement()

/**
 * Form element.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Form(
    @field:JacksonXmlProperty(localName = "fieldset")
    @JacksonXmlElementWrapper(useWrapping = false)
    val fieldsets: List<Fieldset>? = null
) : UIElement()
