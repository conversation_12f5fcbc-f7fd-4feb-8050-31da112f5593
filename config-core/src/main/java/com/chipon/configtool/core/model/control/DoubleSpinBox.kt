package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseUIControl

/**
 * Represents a double spin box UI control element.
 * Maps to <doublespinbox> elements in the XML.
 */
data class DoubleSpinBox(
    @field:JacksonXmlProperty(isAttribute = true)
    override val visible: Boolean? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val size: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val data: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val suffix: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val range: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val default: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val step: String? = null
) : BaseUIControl(visible, define, label, size, data)
