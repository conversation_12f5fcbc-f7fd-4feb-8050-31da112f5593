package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseUIControl

/**
 * Represents a double spin box UI control element.
 * Maps to <doublespinbox> elements in the XML.
 */
data class DoubleSpinBox(
    @JacksonXmlProperty(isAttribute = true)
    override var visible: Boolean? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var size: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var data: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    var suffix: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    var range: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    var default: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    var step: String? = null
) : BaseUIControl(visible, define, label, size, data)
