package com.chipon.configtool.core.model

import com.chipon.configtool.core.model.action.Action
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Represents a timer element.
 * Maps to <timer> elements in the XML.
 */
data class Timer(
    @field:JacksonXmlProperty(isAttribute = true)
    override val define: String,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val visible: Boolean? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val interval: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val singleShot: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val run: String? = null,
    
    @field:JacksonXmlProperty(localName = "action")
    @JacksonXmlElementWrapper(useWrapping = false)
    val actions: List<Action> = emptyList()
) : BaseElement(visible, define)
