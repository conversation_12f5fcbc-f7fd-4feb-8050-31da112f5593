package com.chipon.configtool.core.model

import com.chipon.configtool.core.model.container.VerticalContainer
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement

/**
 * Root element for IFX XML configuration files.
 * Represents the <ifxmlcfg> element with version attribute and child elements.
 */
@JacksonXmlRootElement(localName = "xmlcfg")
data class CfgRoot(
    @field:JacksonXmlProperty(isAttribute = true)
    val version: String,

    field:JacksonXmlProperty(localName = "checksum")
    val checksum: String? = null,

    field:JacksonXmlProperty(localName = "version")
    val versionInfo: Version? = null,

    field:JacksonXmlProperty(localName = "fwversion")
    val fwVersion: FwVersion? = null,

    field:JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer> = emptyList()
)

/**
 * Version information element.
 */
data class Version(
    field:JacksonXmlProperty(isAttribute = true)
    val label: String
)

/**
 * Firmware version element with min and max version constraints.
 */
data class FwVersion(
    field:JacksonXmlProperty(isAttribute = true)
    val min: String,
    
    field:JacksonXmlProperty(isAttribute = true)
    val max: String
)
