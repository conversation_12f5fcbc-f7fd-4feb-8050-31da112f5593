package com.chipon.configtool.core.model

import com.chipon.configtool.core.model.container.VerticalContainer
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement

/**
 * Root element for UI XML configuration files.
 * Represents the <xmlcfg> element with version attribute and child elements.
 */
@JacksonXmlRootElement(localName = "xmlcfg")
class CfgRoot {
    @JacksonXmlProperty(isAttribute = true)
    val version: String? = null

    @JacksonXmlProperty(localName = "version")
    val versionInfo: Version? = null

    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer> = emptyList()
}

class Version {
    @JacksonXmlProperty(isAttribute = true)
    val value: String? = null
}
