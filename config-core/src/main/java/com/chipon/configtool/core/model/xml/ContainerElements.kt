package com.chipon.configtool.core.model.xml

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Vertical container element that arranges child elements vertically.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class VerticalContainer(
    
    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var color: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var sizepolicy: String? = null,
    
    // Child elements - using mixed content approach
    @JacksonXmlProperty(localName = "var")
    @JacksonXmlElementWrapper(useWrapping = false)
    val variables: List<Variable>? = null,
    
    @JacksonXmlProperty(localName = "timer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val timers: List<Timer>? = null,
    
    @JacksonXmlProperty(localName = "math")
    @JacksonXmlElementWrapper(useWrapping = false)
    val mathElements: List<Math>? = null,
    
    @JacksonXmlProperty(localName = "header")
    @JacksonXmlElementWrapper(useWrapping = false)
    val headers: List<Header>? = null,
    
    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null,
    
    @JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer>? = null,
    
    @JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val gridContainers: List<GridContainer>? = null,
    
    @JacksonXmlProperty(localName = "tabcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val tabContainers: List<TabContainer>? = null,
    
    @JacksonXmlProperty(localName = "groupcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val groupContainers: List<GroupContainer>? = null
    
) : ContainerElement()

/**
 * Horizontal container element that arranges child elements horizontally.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class HorizontalContainer(
    
    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var color: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var sizepolicy: String? = null,
    
    // Child elements
    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null,
    
    @JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer>? = null,
    
    @JacksonXmlProperty(localName = "groupcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val groupContainers: List<GroupContainer>? = null,
    
    @JacksonXmlProperty(localName = "button")
    @JacksonXmlElementWrapper(useWrapping = false)
    val buttons: List<Button>? = null,
    
    @JacksonXmlProperty(localName = "text")
    @JacksonXmlElementWrapper(useWrapping = false)
    val textElements: List<Text>? = null,
    
    @JacksonXmlProperty(localName = "led")
    @JacksonXmlElementWrapper(useWrapping = false)
    val leds: List<Led>? = null,
    
    @JacksonXmlProperty(localName = "math")
    @JacksonXmlElementWrapper(useWrapping = false)
    val mathElements: List<Math>? = null,
    
    @JacksonXmlProperty(localName = "checkbox")
    @JacksonXmlElementWrapper(useWrapping = false)
    val checkboxes: List<Checkbox>? = null,
    
    @JacksonXmlProperty(localName = "combo")
    @JacksonXmlElementWrapper(useWrapping = false)
    val combos: List<Combo>? = null,
    
    @JacksonXmlProperty(localName = "doublespinbox")
    @JacksonXmlElementWrapper(useWrapping = false)
    val doubleSpinBoxes: List<DoubleSpinBox>? = null,
    
    @JacksonXmlProperty(localName = "radio")
    @JacksonXmlElementWrapper(useWrapping = false)
    val radios: List<Radio>? = null,
    
    @JacksonXmlProperty(localName = "togglebutton")
    @JacksonXmlElementWrapper(useWrapping = false)
    val toggleButtons: List<ToggleButton>? = null,
    
    @JacksonXmlProperty(localName = "horizontalline")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalLines: List<HorizontalLine>? = null,

    @JacksonXmlProperty(localName = "separator")
    @JacksonXmlElementWrapper(useWrapping = false)
    val separators: List<Separator>? = null,

    @JacksonXmlProperty(localName = "form")
    @JacksonXmlElementWrapper(useWrapping = false)
    val forms: List<Form>? = null

) : ContainerElement()

/**
 * Grid container element that arranges child elements in a grid layout.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class GridContainer(
    
    @JacksonXmlProperty(isAttribute = true)
    override var columns: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var color: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var sizepolicy: String? = null,
    
    // Child elements
    @JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer>? = null,
    
    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null,
    
    @JacksonXmlProperty(localName = "led")
    @JacksonXmlElementWrapper(useWrapping = false)
    val leds: List<Led>? = null,
    
    @JacksonXmlProperty(localName = "text")
    @JacksonXmlElementWrapper(useWrapping = false)
    val textElements: List<Text>? = null,
    
    @JacksonXmlProperty(localName = "button")
    @JacksonXmlElementWrapper(useWrapping = false)
    val buttons: List<Button>? = null
    
) : ContainerElement()

/**
 * Tab container element that provides tabbed interface.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class TabContainer(
    
    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    // Child elements
    @JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer>? = null,
    
    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null
    
) : ContainerElement()

/**
 * Group container element that groups related elements.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class GroupContainer(
    
    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var color: String? = null,
    
    // Child elements
    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null,
    
    @JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer>? = null,
    
    @JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val gridContainers: List<GridContainer>? = null
    
) : ContainerElement()

/**
 * Header element that provides header information.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Header(
    
    @JacksonXmlProperty(isAttribute = true)
    val file: String? = null,
    
    // Child elements
    @JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val gridContainers: List<GridContainer>? = null,
    
    @JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null
    
) : ContainerElement()
