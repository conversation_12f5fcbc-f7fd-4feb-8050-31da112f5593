package com.chipon.configtool.core.model.xml

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * Vertical container element that arranges child elements vertically.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class VerticalContainer(
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var color: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var sizepolicy: String? = null,
    
    // Child elements - using mixed content approach
    @field:JacksonXmlProperty(localName = "var")
    @JacksonXmlElementWrapper(useWrapping = false)
    val variables: List<Variable>? = null,
    
    @field:JacksonXmlProperty(localName = "timer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val timers: List<Timer>? = null,
    
    @field:JacksonXmlProperty(localName = "math")
    @JacksonXmlElementWrapper(useWrapping = false)
    val mathElements: List<Math>? = null,
    
    @field:JacksonXmlProperty(localName = "header")
    @JacksonXmlElementWrapper(useWrapping = false)
    val headers: List<Header>? = null,
    
    @field:JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val gridContainers: List<GridContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "tabcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val tabContainers: List<TabContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "groupcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val groupContainers: List<GroupContainer>? = null
    
) : ContainerElement()

/**
 * Horizontal container element that arranges child elements horizontally.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class HorizontalContainer(
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var color: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var sizepolicy: String? = null,
    
    // Child elements
    @field:JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "groupcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val groupContainers: List<GroupContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "button")
    @JacksonXmlElementWrapper(useWrapping = false)
    val buttons: List<Button>? = null,
    
    @field:JacksonXmlProperty(localName = "text")
    @JacksonXmlElementWrapper(useWrapping = false)
    val textElements: List<Text>? = null,
    
    @field:JacksonXmlProperty(localName = "led")
    @JacksonXmlElementWrapper(useWrapping = false)
    val leds: List<Led>? = null,
    
    @field:JacksonXmlProperty(localName = "math")
    @JacksonXmlElementWrapper(useWrapping = false)
    val mathElements: List<Math>? = null,
    
    @field:JacksonXmlProperty(localName = "checkbox")
    @JacksonXmlElementWrapper(useWrapping = false)
    val checkboxes: List<Checkbox>? = null,
    
    @field:JacksonXmlProperty(localName = "combo")
    @JacksonXmlElementWrapper(useWrapping = false)
    val combos: List<Combo>? = null,
    
    @field:JacksonXmlProperty(localName = "doublespinbox")
    @JacksonXmlElementWrapper(useWrapping = false)
    val doubleSpinBoxes: List<DoubleSpinBox>? = null,
    
    @field:JacksonXmlProperty(localName = "radio")
    @JacksonXmlElementWrapper(useWrapping = false)
    val radios: List<Radio>? = null,
    
    @field:JacksonXmlProperty(localName = "togglebutton")
    @JacksonXmlElementWrapper(useWrapping = false)
    val toggleButtons: List<ToggleButton>? = null,
    
    @field:JacksonXmlProperty(localName = "horizontalline")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalLines: List<HorizontalLine>? = null,

    @field:JacksonXmlProperty(localName = "separator")
    @JacksonXmlElementWrapper(useWrapping = false)
    val separators: List<Separator>? = null,

    @field:JacksonXmlProperty(localName = "form")
    @JacksonXmlElementWrapper(useWrapping = false)
    val forms: List<Form>? = null

) : ContainerElement()

/**
 * Grid container element that arranges child elements in a grid layout.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class GridContainer(
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var columns: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var color: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var sizepolicy: String? = null,
    
    // Child elements
    @field:JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "led")
    @JacksonXmlElementWrapper(useWrapping = false)
    val leds: List<Led>? = null,
    
    @field:JacksonXmlProperty(localName = "text")
    @JacksonXmlElementWrapper(useWrapping = false)
    val textElements: List<Text>? = null,
    
    @field:JacksonXmlProperty(localName = "button")
    @JacksonXmlElementWrapper(useWrapping = false)
    val buttons: List<Button>? = null
    
) : ContainerElement()

/**
 * Tab container element that provides tabbed interface.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class TabContainer(
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    // Child elements
    @field:JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null
    
) : ContainerElement()

/**
 * Group container element that groups related elements.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class GroupContainer(
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override var color: String? = null,
    
    // Child elements
    @field:JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "horizontalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val horizontalContainers: List<HorizontalContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val gridContainers: List<GridContainer>? = null
    
) : ContainerElement()

/**
 * Header element that provides header information.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Header(
    
    @field:JacksonXmlProperty(isAttribute = true)
    val file: String? = null,
    
    // Child elements
    @field:JacksonXmlProperty(localName = "gridcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val gridContainers: List<GridContainer>? = null,
    
    @field:JacksonXmlProperty(localName = "verticalcontainer")
    @JacksonXmlElementWrapper(useWrapping = false)
    val verticalContainers: List<VerticalContainer>? = null
    
) : ContainerElement()
