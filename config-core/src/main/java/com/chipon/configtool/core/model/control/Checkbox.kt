package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseUIControl

/**
 * Represents a checkbox UI control element.
 * Maps to <checkbox> elements in the XML.
 */
data class Checkbox(
    @JacksonXmlProperty(isAttribute = true)
    override var visible: Boolean? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var size: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var data: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    var default: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    var role: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    var lockon: String? = null
) : BaseUIControl(visible, define, label, size, data)
