package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseUIControl

/**
 * Represents a checkbox UI control element.
 * Maps to <checkbox> elements in the XML.
 */
data class Checkbox(
    @field:JacksonXmlProperty(isAttribute = true)
    override val visible: Boolean? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val define: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val label: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val size: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    override val data: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val default: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val role: String? = null,
    
    @field:JacksonXmlProperty(isAttribute = true)
    val lockon: String? = null
) : BaseUIControl(visible, define, label, size, data)
