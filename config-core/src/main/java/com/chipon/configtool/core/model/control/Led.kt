package com.chipon.configtool.core.model.control

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.chipon.configtool.core.model.BaseUIControl

/**
 * Represents a LED UI control element.
 * Maps to <led> elements in the XML.
 */
data class Led(
    @JacksonXmlProperty(isAttribute = true)
    override var visible: Boolean? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var define: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var label: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var size: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    override var data: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    var bitmask: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    var oncolor: String? = null,
    
    @JacksonXmlProperty(isAttribute = true)
    var offcolor: String? = null
) : BaseUIControl(visible, define, label, size, data)
