package com.chipon.configtool.core.model.xml

import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule

/**
 * Simple compilation test to verify our models compile correctly.
 */
object CompilationTest {
    
    fun testBasicInstantiation() {
        // Test that we can create instances of our model classes
        val xmlMapper = XmlMapper().apply {
            registerKotlinModule()
        }
        
        // Test root element
        val root = IfxmlcfgRoot(
            version = "2.0.4",
            checksum = "test-checksum",
            versionInfo = Version(label = "V1.0.0"),
            fwVersion = FwVersion(min = "1.0.0", max = "2.0.0")
        )
        
        // Test container elements
        val verticalContainer = VerticalContainer(
            label = "Test Container",
            color = "255;255;255;255",
            sizepolicy = "fixed;fixed"
        )
        
        val horizontalContainer = HorizontalContainer(
            label = "Test Horizontal"
        )
        
        val gridContainer = GridContainer(
            columns = "2",
            label = "Test Grid"
        )
        
        // Test UI elements
        val variable = Variable(define = "TEST.VAR")
        val timer = Timer(
            define = "TEST_TIMER",
            interval = "1000",
            singleShot = "0",
            run = "1"
        )
        val math = Math(
            define = "TEST_MATH",
            formula = "1+1"
        )
        val button = Button(label = "Test Button", size = "100;25")
        val checkbox = Checkbox(label = "Test Checkbox", define = "TEST.CHECKBOX")
        val combo = Combo(define = "TEST.COMBO")
        val text = Text(label = "Test Text")
        val led = Led(data = "TEST.LED", bitmask = "0x01", oncolor = "green", offcolor = "red")
        
        // Test action
        val action = Action(
            event = "clicked",
            cmd = "sendUSB",
            data = "0x01;0x02;0x03"
        )
        
        println("✓ All model classes instantiated successfully")
        
        // Test basic serialization
        try {
            val xmlString = xmlMapper.writeValueAsString(root)
            println("✓ Basic serialization successful")
            println("Generated XML length: ${xmlString.length} characters")
        } catch (e: Exception) {
            println("✗ Serialization failed: ${e.message}")
        }
    }
    
    fun testInheritance() {
        // Test that inheritance works correctly
        val variable: UIElement = Variable(define = "TEST.VAR")
        val container: ContainerElement = VerticalContainer(label = "Test")
        val element: BaseElement = Timer(define = "TEST_TIMER")
        
        // Test property access through inheritance
        println("Variable define: ${variable.define}")
        println("Container label: ${container.label}")
        println("Element define: ${element.define}")
        
        println("✓ Inheritance hierarchy working correctly")
    }
}

fun main() {
    println("=== Compilation Test ===")
    
    try {
        CompilationTest.testBasicInstantiation()
        CompilationTest.testInheritance()
        println("🎉 All compilation tests passed!")
    } catch (e: Exception) {
        println("❌ Compilation test failed: ${e.message}")
        e.printStackTrace()
    }
}
