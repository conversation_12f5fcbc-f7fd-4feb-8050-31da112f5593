package com.chipon.configtool.core.model.xml

import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import java.io.File

/**
 * Utility class for validating XML model definitions against actual XML files.
 * 
 * <AUTHOR> Validator
 */
object XmlModelValidator {
    
    private val xmlMapper = XmlMapper().apply {
        registerKotlinModule()
    }
    
    /**
     * Validates all XML files in the case directory.
     */
    fun validateAllCaseFiles(): ValidationResult {
        val caseDirectory = File("config-core/src/main/resources/case")
        if (!caseDirectory.exists()) {
            return ValidationResult(false, "Case directory not found: ${caseDirectory.absolutePath}")
        }
        
        val xmlFiles = caseDirectory.listFiles { _, name -> name.endsWith(".xml") }
            ?: return ValidationResult(false, "No XML files found in case directory")
        
        val results = mutableListOf<String>()
        var allSuccessful = true
        
        xmlFiles.forEach { xmlFile ->
            try {
                val ifxmlcfg = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
                results.add("✓ Successfully parsed ${xmlFile.name}")
                
                // Basic validation
                if (ifxmlcfg.version == null) {
                    results.add("  ⚠ Warning: Missing version attribute in ${xmlFile.name}")
                }
                if (ifxmlcfg.versionInfo == null) {
                    results.add("  ⚠ Warning: Missing version info in ${xmlFile.name}")
                }
                if (ifxmlcfg.fwVersion == null) {
                    results.add("  ⚠ Warning: Missing firmware version in ${xmlFile.name}")
                }
                
            } catch (e: Exception) {
                allSuccessful = false
                results.add("✗ Failed to parse ${xmlFile.name}: ${e.message}")
                e.printStackTrace()
            }
        }
        
        return ValidationResult(allSuccessful, results.joinToString("\n"))
    }
    
    /**
     * Validates a specific XML file.
     */
    fun validateFile(fileName: String): ValidationResult {
        val xmlFile = File("config-core/src/main/resources/case/$fileName")
        if (!xmlFile.exists()) {
            return ValidationResult(false, "File not found: ${xmlFile.absolutePath}")
        }
        
        return try {
            val ifxmlcfg = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
            
            val details = buildString {
                appendLine("Successfully parsed $fileName")
                appendLine("Version: ${ifxmlcfg.version}")
                appendLine("Version Info: ${ifxmlcfg.versionInfo?.label}")
                appendLine("FW Version: ${ifxmlcfg.fwVersion?.min} - ${ifxmlcfg.fwVersion?.max}")
                appendLine("Checksum: ${ifxmlcfg.checksum}")
                
                if (ifxmlcfg.svd != null) {
                    appendLine("SVD File: ${ifxmlcfg.svd?.file}")
                }
                
                if (ifxmlcfg.loadSaveAllowed != null) {
                    appendLine("Load/Save Allowed: ${ifxmlcfg.loadSaveAllowed?.value}")
                }
                
                ifxmlcfg.verticalContainers?.let { containers ->
                    appendLine("Vertical Containers: ${containers.size}")
                    containers.forEach { container ->
                        appendLine("  - Container: color=${container.color}, sizepolicy=${container.sizepolicy}")
                        container.variables?.let { vars ->
                            appendLine("    Variables: ${vars.size}")
                        }
                        container.timers?.let { timers ->
                            appendLine("    Timers: ${timers.size}")
                        }
                        container.mathElements?.let { math ->
                            appendLine("    Math Elements: ${math.size}")
                        }
                        container.headers?.let { headers ->
                            appendLine("    Headers: ${headers.size}")
                            headers.forEach { header ->
                                appendLine("      - Header file: ${header.file}")
                            }
                        }
                    }
                }
            }
            
            ValidationResult(true, details)
            
        } catch (e: Exception) {
            ValidationResult(false, "Failed to parse $fileName: ${e.message}\n${e.stackTraceToString()}")
        }
    }
    
    /**
     * Tests round-trip serialization/deserialization.
     */
    fun testRoundTrip(fileName: String): ValidationResult {
        val xmlFile = File("config-core/src/main/resources/case/$fileName")
        if (!xmlFile.exists()) {
            return ValidationResult(false, "File not found: ${xmlFile.absolutePath}")
        }
        
        return try {
            // Parse original
            val original = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
            
            // Serialize to XML string
            val serialized = xmlMapper.writeValueAsString(original)
            
            // Parse serialized version
            val roundTrip = xmlMapper.readValue(serialized, IfxmlcfgRoot::class.java)
            
            // Compare key properties
            val issues = mutableListOf<String>()
            
            if (original.version != roundTrip.version) {
                issues.add("Version mismatch: ${original.version} vs ${roundTrip.version}")
            }
            
            if (original.checksum != roundTrip.checksum) {
                issues.add("Checksum mismatch: ${original.checksum} vs ${roundTrip.checksum}")
            }
            
            if (original.versionInfo?.label != roundTrip.versionInfo?.label) {
                issues.add("Version label mismatch: ${original.versionInfo?.label} vs ${roundTrip.versionInfo?.label}")
            }
            
            if (issues.isEmpty()) {
                ValidationResult(true, "Round-trip test successful for $fileName")
            } else {
                ValidationResult(false, "Round-trip test failed for $fileName:\n${issues.joinToString("\n")}")
            }
            
        } catch (e: Exception) {
            ValidationResult(false, "Round-trip test failed for $fileName: ${e.message}")
        }
    }
}

/**
 * Result of validation operation.
 */
data class ValidationResult(
    val success: Boolean,
    val message: String
)

/**
 * Main function for testing the XML models.
 */
fun main() {
    println("=== XML Model Validation ===")
    println()
    
    // Test all files
    println("Testing all XML files:")
    val allResult = XmlModelValidator.validateAllCaseFiles()
    println(allResult.message)
    println()
    
    // Test specific files in detail
    val testFiles = listOf("ICW_TLE9263.xml", "ICW_TLE9273.xml", "ICW_TLE94x1.xml", "ICW_TLE9263RV.xml")
    
    testFiles.forEach { fileName ->
        println("=== Detailed validation for $fileName ===")
        val result = XmlModelValidator.validateFile(fileName)
        println(result.message)
        println()
        
        if (result.success) {
            println("=== Round-trip test for $fileName ===")
            val roundTripResult = XmlModelValidator.testRoundTrip(fileName)
            println(roundTripResult.message)
            println()
        }
    }
}
