package com.chipon.configtool.core.model.xml

import com.chipon.configtool.core.model.IfxmlcfgRoot
import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.module.kotlin.kotlinModule
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import java.io.File

/**
 * Test class for validating the Jackson XML model definitions against actual XML files.
 *
 * <AUTHOR> Test
 */
class IfxmlcfgModelTest : FunSpec({

    val xmlMapper = XmlMapper().apply {
        registerModule(kotlinModule())
    }
    
    val caseDirectory = File("src/main/resources/case")
    
    test("should deserialize ICW_TLE9263.xml successfully") {
        val xmlFile = File(caseDirectory, "ICW_TLE9263.xml")
        xmlFile.exists() shouldBe true
        
        val ifxmlcfg = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
        
        // Validate root element
        ifxmlcfg shouldNotBe null
        ifxmlcfg.version shouldBe "2.0.4"
        ifxmlcfg.checksum shouldNotBe null
        ifxmlcfg.checksum shouldContain "1c447b2dc16f1d4cd77fbdba2ba704fac76e20939c36ce8238f475bbcf380392"
        
        // Validate version info
        ifxmlcfg.versionInfo shouldNotBe null
        ifxmlcfg.versionInfo?.label shouldBe "V0.0.5"
        
        // Validate firmware version
        ifxmlcfg.fwVersion shouldNotBe null
        ifxmlcfg.fwVersion?.min shouldBe "2.2.1"
        ifxmlcfg.fwVersion?.max shouldBe "*.*.*"
        
        // Validate vertical containers
        ifxmlcfg.verticalContainers shouldNotBe null
        ifxmlcfg.verticalContainers?.size shouldBe 1
        
        val mainContainer = ifxmlcfg.verticalContainers?.first()
        mainContainer shouldNotBe null
        mainContainer?.color shouldBe "255;255;255;255"
        mainContainer?.sizepolicy shouldBe "fixed;fixed"
        
        // Validate variables
        mainContainer?.variables shouldNotBe null
        mainContainer?.variables?.size?.let { it > 0 } shouldBe true
        
        // Check for specific variables
        val uioVersionVar = mainContainer?.variables?.find { it.define == "UIO.VERSION" }
        uioVersionVar shouldNotBe null
        
        // Validate timers
        mainContainer?.timers shouldNotBe null
        mainContainer?.timers?.size?.let { it > 0 } shouldBe true
        
        // Check for INIT timer
        val initTimer = mainContainer?.timers?.find { it.define == "INIT" }
        initTimer shouldNotBe null
        initTimer?.interval shouldBe "1"
        initTimer?.singleShot shouldBe "1"
        initTimer?.run shouldBe "1"
        initTimer?.actions shouldNotBe null
        
        // Validate math elements
        mainContainer?.mathElements shouldNotBe null
        mainContainer?.mathElements?.size?.let { it > 0 } shouldBe true
        
        // Validate headers
        mainContainer?.headers shouldNotBe null
        mainContainer?.headers?.size shouldBe 1
        
        val header = mainContainer?.headers?.first()
        header?.file shouldBe "SBC_TLE9263.h"
    }
    
    test("should deserialize ICW_TLE9273.xml successfully") {
        val xmlFile = File(caseDirectory, "ICW_TLE9273.xml")
        xmlFile.exists() shouldBe true
        
        val ifxmlcfg = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
        
        // Validate root element
        ifxmlcfg shouldNotBe null
        ifxmlcfg.version shouldBe "2.0.4"
        ifxmlcfg.versionInfo?.label shouldBe "V0.0.7"
        ifxmlcfg.fwVersion?.min shouldBe "2.2.1"
        ifxmlcfg.fwVersion?.max shouldBe "*.*.*"
    }
    
    test("should deserialize ICW_TLE94x1.xml successfully") {
        val xmlFile = File(caseDirectory, "ICW_TLE94x1.xml")
        xmlFile.exists() shouldBe true
        
        val ifxmlcfg = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
        
        // Validate root element
        ifxmlcfg shouldNotBe null
        ifxmlcfg.version shouldBe "2.0.3"
        ifxmlcfg.versionInfo?.label shouldBe "V0.0.9"
    }
    
    test("should deserialize ICW_TLE9263RV.xml successfully") {
        val xmlFile = File(caseDirectory, "ICW_TLE9263RV.xml")
        xmlFile.exists() shouldBe true
        
        val ifxmlcfg = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
        
        // Validate root element
        ifxmlcfg shouldNotBe null
        ifxmlcfg.version shouldBe "2.0.15"
        ifxmlcfg.versionInfo?.label shouldBe "V1.0.1"
        
        // This file should have SVD reference
        ifxmlcfg.svd shouldNotBe null
        ifxmlcfg.svd?.file shouldBe "ICW_TLE9263/TLE9263.svd"
        
        // This file should have load/save allowed
        ifxmlcfg.loadSaveAllowed shouldNotBe null
        ifxmlcfg.loadSaveAllowed?.value shouldBe "0"
    }
    
    test("should serialize and deserialize round trip") {
        val xmlFile = File(caseDirectory, "ICW_TLE9263.xml")
        val originalIfxmlcfg = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
        
        // Serialize back to XML
        val serializedXml = xmlMapper.writeValueAsString(originalIfxmlcfg)
        
        // Deserialize again
        val deserializedIfxmlcfg = xmlMapper.readValue(serializedXml, IfxmlcfgRoot::class.java)
        
        // Compare key properties
        deserializedIfxmlcfg.version shouldBe originalIfxmlcfg.version
        deserializedIfxmlcfg.checksum shouldBe originalIfxmlcfg.checksum
        deserializedIfxmlcfg.versionInfo?.label shouldBe originalIfxmlcfg.versionInfo?.label
        deserializedIfxmlcfg.fwVersion?.min shouldBe originalIfxmlcfg.fwVersion?.min
        deserializedIfxmlcfg.fwVersion?.max shouldBe originalIfxmlcfg.fwVersion?.max
    }
    
    test("should handle all XML files in case directory") {
        val xmlFiles = caseDirectory.listFiles { _, name -> name.endsWith(".xml") }
        xmlFiles shouldNotBe null
        xmlFiles!!.size shouldBe 8
        
        xmlFiles.forEach { xmlFile ->
            println("Testing file: ${xmlFile.name}")
            
            val ifxmlcfg = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
            
            // Basic validation for all files
            ifxmlcfg shouldNotBe null
            ifxmlcfg.version shouldNotBe null
            ifxmlcfg.versionInfo shouldNotBe null
            ifxmlcfg.fwVersion shouldNotBe null
            
            println("Successfully parsed ${xmlFile.name}")
        }
    }
})
