package com.chipon.configtool.core.model

import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import java.io.File

class IfxmlcfgModelTest : StringSpec({
    
    val xmlMapper = XmlMapper().apply {
        registerModule(KotlinModule.Builder().build())
    }
    
    "should parse ICW_TLE9263.xml successfully" {
        val xmlFile = File("src/main/resources/case/ICW_TLE9263.xml")
        val ifxmlcfg = xmlMapper.readValue(xmlFile, IfxmlcfgRoot::class.java)
        
        ifxmlcfg shouldNotBe null
        ifxmlcfg.version shouldBe "2.0.4"
        ifxmlcfg.checksum shouldNotBe null
        ifxmlcfg.versionInfo?.label shouldBe "V0.0.5"
        ifxmlcfg.fwVersion?.min shouldBe "2.2.1"
        ifxmlcfg.fwVersion?.max shouldBe "*.*.*"
        ifxmlcfg.verticalContainers.size shouldBe 1
        
        val verticalContainer = ifxmlcfg.verticalContainers.first()
        verticalContainer.color shouldBe "255;255;255;255"
        verticalContainer.sizepolicy shouldBe "fixed;fixed"
        verticalContainer.variables.size shouldBe 18 // Based on the XML content
        verticalContainer.timers.size shouldBe 2
        verticalContainer.mathElements.size shouldBe 15
        verticalContainer.headers.size shouldBe 1
    }
    
    "should serialize and deserialize correctly" {
        val original = IfxmlcfgRoot(
            version = "2.0.4",
            checksum = "test-checksum",
            versionInfo = Version("V1.0.0"),
            fwVersion = FwVersion("1.0.0", "2.0.0")
        )
        
        val xml = xmlMapper.writeValueAsString(original)
        val deserialized = xmlMapper.readValue(xml, IfxmlcfgRoot::class.java)
        
        deserialized.version shouldBe original.version
        deserialized.checksum shouldBe original.checksum
        deserialized.versionInfo?.label shouldBe original.versionInfo?.label
        deserialized.fwVersion?.min shouldBe original.fwVersion?.min
        deserialized.fwVersion?.max shouldBe original.fwVersion?.max
    }
})
