@echo off
echo Testing XML Model Compilation...
echo.

echo Running Kotlin compilation for config-core...
gradlew.bat :config-core:compileKotlin

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Compilation successful!
    echo.
    echo Running tests...
    gradlew.bat :config-core:test --tests "IfxmlcfgModelTest"
) else (
    echo.
    echo ✗ Compilation failed!
    echo Check the errors above.
)

pause
